#!/bin/bash

# Development Authentication Setup
# Creates test user and sets up environment for easy local development

echo "🔧 Setting up development authentication..."

# Ensure Supabase is running
if ! docker ps | grep -q "supabase_kong"; then
    echo "⚠️  Supabase is not running. Starting..."
    supabase start
fi

# Create test user
VITE_SUPABASE_URL=http://127.0.0.1:54321 \
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU \
node scripts/create-test-user.js

echo ""
echo "🎯 Quick Start:"
echo "1. Run: npm run dev"
echo "2. Open: http://127.0.0.1:5175"
echo "3. Login with:"
echo "   Email: <EMAIL>"
echo "   Password: testuser123"