-- Migration: Add sensor threshold columns
-- Date: 2025-01-29
-- Purpose: Fix schema mismatch where application code expects threshold columns
--          but database uses custom_* column names

-- This migration adds the missing threshold columns that the application code expects:
-- - temp_min_threshold, temp_max_threshold, humidity_min_threshold, humidity_max_threshold
--
-- The migration preserves backward compatibility by keeping existing custom_* columns
-- and migrates data from the old columns to the new ones.

-- Add new threshold columns to sensors table
ALTER TABLE sensors
ADD COLUMN IF NOT EXISTS temp_min_threshold DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS temp_max_threshold DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS humidity_min_threshold DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS humidity_max_threshold DECIMAL(5,2);

-- Migrate data from existing custom_* columns to new threshold columns
-- For temperature thresholds, prioritize Celsius values as the primary source
-- Use COALESCE to preserve existing non-null values in threshold columns
UPDATE sensors
SET
    temp_min_threshold = COALESCE(
        temp_min_threshold,  -- Preserve existing value if already populated
        custom_temp_min_celsius,
        -- Fallback: convert Fahrenheit to Celsius if Celsius is null
        CASE
            WHEN custom_temp_min_fahrenheit IS NOT NULL
            THEN ROUND((custom_temp_min_fahrenheit - 32) * 5.0 / 9.0, 2)
            ELSE NULL
        END
    ),
    temp_max_threshold = COALESCE(
        temp_max_threshold,  -- Preserve existing value if already populated
        custom_temp_max_celsius,
        -- Fallback: convert Fahrenheit to Celsius if Celsius is null
        CASE
            WHEN custom_temp_max_fahrenheit IS NOT NULL
            THEN ROUND((custom_temp_max_fahrenheit - 32) * 5.0 / 9.0, 2)
            ELSE NULL
        END
    ),
    humidity_min_threshold = COALESCE(humidity_min_threshold, custom_humidity_min),
    humidity_max_threshold = COALESCE(humidity_max_threshold, custom_humidity_max);

-- Create indexes on new threshold columns for optimal query performance
-- These indexes will improve performance for temperature monitoring and alert queries
CREATE INDEX IF NOT EXISTS idx_sensors_temp_min_threshold ON sensors(temp_min_threshold)
WHERE temp_min_threshold IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_sensors_temp_max_threshold ON sensors(temp_max_threshold)
WHERE temp_max_threshold IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_sensors_humidity_min_threshold ON sensors(humidity_min_threshold)
WHERE humidity_min_threshold IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_sensors_humidity_max_threshold ON sensors(humidity_max_threshold)
WHERE humidity_max_threshold IS NOT NULL;

-- Add comments to document the new columns
COMMENT ON COLUMN sensors.temp_min_threshold IS 'Minimum temperature threshold in Celsius for alerts';
COMMENT ON COLUMN sensors.temp_max_threshold IS 'Maximum temperature threshold in Celsius for alerts';
COMMENT ON COLUMN sensors.humidity_min_threshold IS 'Minimum humidity threshold percentage for alerts';
COMMENT ON COLUMN sensors.humidity_max_threshold IS 'Maximum humidity threshold percentage for alerts';

-- Note: The existing custom_* columns are preserved for backward compatibility
-- Future migrations may remove these columns once all code references are updated