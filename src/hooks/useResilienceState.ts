import { useEffect, useMemo, useState } from 'react';
import type { ResilienceMode } from '@/lib/ResilienceCoordinator';
import { resilienceCoordinator } from '@/lib/ResilienceCoordinator';

export type ServiceResilienceState = {
  status: 'healthy' | 'degraded' | 'offline';
  reason?: string;
  circuitOpen?: boolean;
  failures?: number;
  recoveryEtaMs?: number;
  lastUpdated: number;
};

export type UnifiedResilienceState = {
  mode: ResilienceMode;
  services: Record<'tempstick' | 'voice' | 'database', ServiceResilienceState>;
  degradedCapabilities: string[];
  offlineStorage: {
    persistentSupported: boolean;
    quotaBytes?: number;
    usageBytes?: number;
    lastCleanup?: number;
  };
  updatedAt: number;
};

type HookOptions = {
  subscribe?: boolean;
};

const defaultState: UnifiedResilienceState = {
  mode: 'full_functionality',
  services: {
    tempstick: {
      status: 'healthy',
      lastUpdated: Date.now(),
    },
    voice: {
      status: 'healthy',
      lastUpdated: Date.now(),
    },
    database: {
      status: 'healthy',
      lastUpdated: Date.now(),
    },
  },
  degradedCapabilities: [],
  offlineStorage: {
    persistentSupported: false,
  },
  updatedAt: Date.now(),
};

export function useResilienceState({ subscribe = true }: HookOptions = {}): UnifiedResilienceState {
  const [state, setState] = useState<UnifiedResilienceState>(() => resilienceCoordinator.getState() ?? defaultState);

  useEffect(() => {
    if (!subscribe) {
      void resilienceCoordinator.refreshOfflineStatus();
      return;
    }
    const unsubscribe = resilienceCoordinator.subscribe((nextState) => {
      setState(nextState);
    });
    return unsubscribe;
  }, [subscribe]);

  return useMemo(() => state, [state]);
}

export function useServiceResilience(service: 'tempstick' | 'voice' | 'database'): ServiceResilienceState {
  const state = useResilienceState();
  return state.services[service];
}
