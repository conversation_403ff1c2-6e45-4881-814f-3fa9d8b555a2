import { useCallback, useEffect, useMemo, useState } from 'react';
import type { RealtimeChannel } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import useSensorStatuses from '@/hooks/useSensorStatuses';
import useLotLocationData from '@/hooks/useLotLocationData';
import { cToF } from '@/lib/units';
import type {
  AgingAlert,
  LotLocationData,
  TemperatureViolation,
  TemperatureWithLotContext,
} from '@/types/tempstick';

interface SensorMetadata {
  internalId: string;
  storageAreaId: string | null;
  storageAreaName: string | null;
  locationDescription: string | null;
  tempMinThreshold: number | null;
  tempMaxThreshold: number | null;
  humidityMinThreshold: number | null;
  humidityMaxThreshold: number | null;
  batteryLevel: number | null;
  signalStrength: number | null;
  lastSeenAt: string | null;
}

interface SupabaseSensorRow {
  id: string;
  sensor_id: string;
  name: string | null;
  location_description: string | null;
  storage_area_id: string | null;
  custom_temp_min_celsius: number | null;
  custom_temp_max_celsius: number | null;
  custom_humidity_min: number | null;
  custom_humidity_max: number | null;
  battery_level: number | null;
  signal_strength: number | null;
  last_seen_at: string | null;
}

interface SupabaseStorageAreaRow {
  id: string;
  name: string;
}

const buildAgingAlert = (
  lot: LotLocationData,
  sensorId: string,
  storageAreaName: string | null
): AgingAlert => {
  const severity: AgingAlert['severity'] = lot.isExpired
    ? 'critical'
    : lot.ageStatus === 'critical'
    ? 'critical'
    : lot.ageStatus === 'warning' || lot.isApproachingExpiry
    ? 'warning'
    : 'info';

  const statusDescriptor = lot.isExpired
    ? 'has expired'
    : lot.isApproachingExpiry
    ? 'is approaching expiry'
    : lot.ageStatus === 'critical'
    ? 'is past the critical age threshold'
    : lot.ageStatus === 'warning'
    ? 'is approaching the aging threshold'
    : 'is being monitored';

  return {
    id: `${lot.lotId}-${sensorId}-${severity}`,
    lotId: lot.lotId,
    lotNumber: lot.lotNumber,
    sensorId,
    storageAreaId: lot.storageAreaId,
    storageAreaName,
    severity,
    message: `Lot ${lot.lotNumber} in ${storageAreaName ?? 'unknown storage'} ${statusDescriptor}.`,
    createdAt: new Date().toISOString(),
    ageInDays: lot.ageInDays,
    expiryDate: lot.expiryDate,
  };
};

const buildTemperatureViolation = (
  lot: LotLocationData | null,
  sensorId: string,
  storageAreaName: string | null,
  readingCelsius: number,
  threshold: { type: 'high' | 'low'; value: number }
): TemperatureViolation => {
  const direction = threshold.type === 'high' ? 'above' : 'below';
  const readingFahrenheit = cToF(readingCelsius);
  const thresholdFahrenheit = cToF(threshold.value);

  return {
    id: `${lot?.lotId ?? 'unassigned'}-${sensorId}-${threshold.type}`,
    lotId: lot?.lotId ?? null,
    lotNumber: lot?.lotNumber ?? null,
    batchId: lot?.batchId ?? null,
    batchNumber: lot?.batchNumber ?? null,
    sensorId,
    storageAreaId: lot?.storageAreaId ?? null,
    storageAreaName,
    severity: threshold.type === 'high' ? 'critical' : 'warning',
    message: lot
      ? `Sensor ${sensorId} in ${storageAreaName ?? 'unknown storage'} is ${direction} safe limits impacting lot ${lot.lotNumber}.`
      : `Sensor ${sensorId} in ${storageAreaName ?? 'unknown storage'} is ${direction} safe limits and may impact stored lots.`,
    recordedAt: new Date().toISOString(),
    readingFahrenheit,
    thresholdFahrenheit,
    direction,
  };
};

const mapSensorMetadata = (
  sensors: SupabaseSensorRow[],
  storageAreas: Map<string, SupabaseStorageAreaRow>
): Map<string, SensorMetadata> => {
  return new Map(
    sensors.map((sensor) => {
      const storageArea = sensor.storage_area_id ? storageAreas.get(sensor.storage_area_id) : null;
      const metadata: SensorMetadata = {
        internalId: sensor.id,
        storageAreaId: sensor.storage_area_id,
        storageAreaName: storageArea?.name ?? null,
        locationDescription: sensor.location_description ?? null,
        tempMinThreshold: sensor.custom_temp_min_celsius,
        tempMaxThreshold: sensor.custom_temp_max_celsius,
        humidityMinThreshold: sensor.custom_humidity_min,
        humidityMaxThreshold: sensor.custom_humidity_max,
        batteryLevel: sensor.battery_level,
        signalStrength: sensor.signal_strength,
        lastSeenAt: sensor.last_seen_at,
      };

      return [sensor.sensor_id, metadata];
    })
  );
};

export const useTemperatureWithLots = () => {
  const {
    sensorStatuses,
    loading: sensorLoading,
    error: sensorError,
    lastUpdated,
    refresh: refreshSensors,
  } = useSensorStatuses();
  const {
    lots: lotLocations,
    loading: lotLoading,
    error: lotError,
    refresh: refreshLots,
    getLotsInStorageArea,
    getLotsApproachingExpiry,
  } = useLotLocationData();

  const [metadataLoading, setMetadataLoading] = useState<boolean>(true);
  const [metadataError, setMetadataError] = useState<string | null>(null);
  const [sensorMetadata, setSensorMetadata] = useState<Map<string, SensorMetadata>>(new Map());

  const fetchSensorMetadata = useCallback(async () => {
    setMetadataLoading(true);
    setMetadataError(null);

    try {
      const [sensorsResponse, storageAreasResponse] = await Promise.all([
        supabase
          .from('sensors')
          .select(
            'id, sensor_id, name, location_description, storage_area_id, custom_temp_min_celsius, custom_temp_max_celsius, custom_humidity_min, custom_humidity_max, battery_level, signal_strength, last_seen_at'
          ),
        supabase.from('storage_areas').select('id, name'),
      ]);

      if (sensorsResponse.error) throw sensorsResponse.error;
      if (storageAreasResponse.error) throw storageAreasResponse.error;

      const storageAreas = new Map<string, SupabaseStorageAreaRow>(
        (storageAreasResponse.data ?? []).map((area: SupabaseStorageAreaRow) => [area.id, area])
      );

      const metadata = mapSensorMetadata((sensorsResponse.data ?? []) as SupabaseSensorRow[], storageAreas);
      setSensorMetadata(metadata);
    } catch (err) {
      console.error('Failed to fetch sensor metadata:', err);
      setMetadataError(err instanceof Error ? err.message : 'Failed to load sensor metadata');
      setSensorMetadata(new Map());
    } finally {
      setMetadataLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSensorMetadata();
  }, [fetchSensorMetadata]);

  useEffect(() => {
    let channel: RealtimeChannel | null = null;

    const setupRealtime = async () => {
      channel = supabase
        .channel('temperature-with-lots')
        .on('postgres_changes', { event: '*', schema: 'public', table: 'sensors' }, fetchSensorMetadata)
        .subscribe();
    };

    setupRealtime();

    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [fetchSensorMetadata]);

  const sensorsWithLots: TemperatureWithLotContext[] = useMemo(() => {
    return sensorStatuses.map((status) => {
      const metadata = sensorMetadata.get(status.sensor.id);
      const storageAreaId = metadata?.storageAreaId ?? null;
      const storageAreaName = metadata?.storageAreaName ?? null;
      const lotsForSensor = lotLocations.filter((lot) => {
        const matchesStorageArea = storageAreaId ? lot.storageAreaId === storageAreaId : false;
        const matchesSensor = lot.sensorExternalId === status.sensor.id;
        return matchesStorageArea || matchesSensor;
      });

      const agingAlerts: AgingAlert[] = lotsForSensor
        .filter((lot) => lot.ageStatus !== 'fresh' || lot.isApproachingExpiry || lot.isExpired)
        .map((lot) => buildAgingAlert(lot, status.sensor.id, storageAreaName));

      const temperatureViolations: TemperatureViolation[] = [];
      if (status.latestReading?.temperature != null && metadata) {
        const reading = status.latestReading.temperature;
        if (metadata.tempMaxThreshold != null && reading > metadata.tempMaxThreshold) {
          const violations =
            lotsForSensor.length > 0
              ? lotsForSensor.map((lot) =>
                  buildTemperatureViolation(lot, status.sensor.id, storageAreaName, reading, {
                    type: 'high',
                    value: metadata.tempMaxThreshold!,
                  })
                )
              : [
                  buildTemperatureViolation(null, status.sensor.id, storageAreaName, reading, {
                    type: 'high',
                    value: metadata.tempMaxThreshold!,
                  }),
                ];
          temperatureViolations.push(...violations);
        }
        if (metadata.tempMinThreshold != null && reading < metadata.tempMinThreshold) {
          const violations =
            lotsForSensor.length > 0
              ? lotsForSensor.map((lot) =>
                  buildTemperatureViolation(lot, status.sensor.id, storageAreaName, reading, {
                    type: 'low',
                    value: metadata.tempMinThreshold!,
                  })
                )
              : [
                  buildTemperatureViolation(null, status.sensor.id, storageAreaName, reading, {
                    type: 'low',
                    value: metadata.tempMinThreshold!,
                  }),
                ];
          temperatureViolations.push(...violations);
        }
      }

      return {
        sensorId: status.sensor.id,
        sensorInternalId: metadata?.internalId ?? null,
        sensorName: status.sensor.name,
        sensorLocation: metadata?.locationDescription ?? status.sensor.location,
        status: status.isOnline ? 'online' : 'offline',
        storageAreaId,
        storageAreaName,
        latestReading: status.latestReading,
        lots: lotsForSensor,
        agingAlerts,
        temperatureViolations,
        batteryLevel: metadata?.batteryLevel ?? null,
        signalStrength: metadata?.signalStrength ?? null,
        humidityThresholds: {
          min: metadata?.humidityMinThreshold ?? null,
          max: metadata?.humidityMaxThreshold ?? null,
        },
        temperatureThresholds: {
          min: metadata?.tempMinThreshold ?? null,
          max: metadata?.tempMaxThreshold ?? null,
        },
        lastUpdated,
        lastSeenAt: metadata?.lastSeenAt ?? status.lastUpdated,
      } satisfies TemperatureWithLotContext;
    });
  }, [lastUpdated, lotLocations, sensorMetadata, sensorStatuses]);

  const allAgingAlerts: AgingAlert[] = useMemo(
    () => sensorsWithLots.flatMap((ctx) => ctx.agingAlerts),
    [sensorsWithLots]
  );

  const allTemperatureViolations: TemperatureViolation[] = useMemo(
    () => sensorsWithLots.flatMap((ctx) => ctx.temperatureViolations),
    [sensorsWithLots]
  );

  const getTemperatureViolationsForLot = useCallback(
    (lotId: string) =>
      sensorsWithLots
        .flatMap((ctx) => ctx.temperatureViolations)
        .filter((violation) => violation.lotId === lotId),
    [sensorsWithLots]
  );

  const getTemperatureViolationsForBatch = useCallback(
    (batchId: string) =>
      sensorsWithLots
        .flatMap((ctx) => ctx.temperatureViolations)
        .filter((violation) => violation.batchId === batchId),
    [sensorsWithLots]
  );

  const getTemperatureForLot = useCallback(
    (lotId: string) => {
      const context = sensorsWithLots.find((ctx) => ctx.lots.some((lot) => lot.lotId === lotId));
      return context?.latestReading ?? null;
    },
    [sensorsWithLots]
  );

  const getAgingAlertsForSensor = useCallback(
    (sensorId: string) =>
      sensorsWithLots
        .flatMap((ctx) => ctx.agingAlerts)
        .filter((alert) => alert.sensorId === sensorId),
    [sensorsWithLots]
  );

  const refresh = useCallback(async () => {
    await Promise.all([refreshSensors(), refreshLots(), fetchSensorMetadata()]);
  }, [fetchSensorMetadata, refreshLots, refreshSensors]);

  const loading = sensorLoading || lotLoading || metadataLoading;
  const error = sensorError?.message ?? lotError ?? metadataError;

  return {
    sensorsWithLots,
    lots: lotLocations,
    loading,
    error,
    agingAlerts: allAgingAlerts,
    temperatureViolations: allTemperatureViolations,
    refresh,
    getLotsInStorageArea,
    getLotsApproachingExpiry,
    getTemperatureViolationsForLot,
    getAgingAlertsForSensor,
    getTemperatureForLot,
    getTemperatureViolationsForBatch,
  };
};

export default useTemperatureWithLots;
