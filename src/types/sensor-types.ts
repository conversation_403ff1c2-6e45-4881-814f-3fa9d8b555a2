// Sensor-related type definitions for TypeScript errors
// Based on compilation errors showing missing properties

export interface TempStickSensor {
  id: string;
  sensor_id: string;
  name: string;
  status: string;
  location: string;
  tempstick_sensor_id: string;
  created_at: string;
}

export interface Sensor {
  id: string;
  sensor_id: string;
  tempstick_sensor_id: string;
  name: string;
  location?: string;
  location_description?: string | null;
  sensor_type: 'temperature_humidity' | 'temperature_only' | 'humidity_only';
  temp_min_threshold: number | null;
  temp_max_threshold: number | null;
  humidity_min_threshold?: number | null;
  humidity_max_threshold?: number | null;
  storage_area_id: string | null;
  active: boolean;
  is_active?: boolean;
  is_online?: boolean;
  battery_level?: number | null;
  signal_strength?: number | null;
  last_seen_at?: string | null;
  next_maintenance_due?: string | null;
  maintenance_notes?: string | null;
  installation_date?: string | null;
  calibration_date?: string | null;
  last_calibrated_at?: string | null;
  created_at: string;

  // Relations
  storage_areas?: StorageArea | null;
}

export interface StorageArea {
  id: string;
  name: string;
  area_type: 'freezer' | 'refrigerator' | 'dry_storage' | 'processing' | 'receiving' | 'other';
  required_temp_min: number | null;
  required_temp_max: number | null;
  haccp_control_point: boolean;
  description?: string | null;
  created_at: string;
}

export interface SimpleAlert {
  id: string;
  sensor_id: string;
  alert_type: string;
  severity: 'low' | 'warning' | 'high';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

// Export types for use in other files
export type { TempStickSensor as default };
