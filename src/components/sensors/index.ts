/**
 * Sensor Components Export Index
 *
 * Centralized exports for all temperature monitoring and sensor components
 */

// Dashboard and monitoring components
export { TemperatureDashboard } from './TemperatureDashboard';
export { TempStickStyleDashboard } from './TempStickStyleDashboard';
export { Temperature<PERSON>hart } from './TemperatureChart';
export { SensorStatusCard } from './SensorStatusCard';

// Management and configuration components
export { SensorManagement } from './SensorManagement';
export { SensorDiscovery } from './SensorDiscovery';
export { SensorCalibration } from './SensorCalibration';
export { SensorErrorDisplay, default as SensorErrorDisplayDefault } from './SensorErrorDisplay';

// Re-export the custom hook for convenience
export { useTemperatureDashboard } from '../../hooks/useTemperatureDashboard';

// Note: Local component prop types are internal to their files.
// Export them explicitly from each component if needed in the future.
