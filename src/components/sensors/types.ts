export type SensorErrorSeverity = 'info' | 'warning' | 'error' | 'critical';

export type SensorErrorSource = 'tempstick-api' | 'supabase' | 'ui' | 'unknown';

export interface SensorError {
  id: string;
  title: string;
  userMessage: string;
  actionSteps: string[];
  severity: SensorErrorSeverity;
  source: SensorErrorSource;
  retryable: boolean;
  occurredAt: string;
  operation?: string;
  correlationId?: string;
  statusCode?: number;
  supportUrl?: string;
  developerDetails?: string;
  metadata?: Record<string, unknown>;
}
