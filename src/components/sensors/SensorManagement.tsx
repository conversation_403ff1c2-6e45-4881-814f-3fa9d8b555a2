/**
 * Sensor Management Interface Component
 *
 * Comprehensive sensor configuration and management with:
 * - Sensor discovery and automatic registration
 * - Configuration forms for thresholds and settings
 * - Storage area assignment with drag-and-drop
 * - Calibration tracking and maintenance scheduling
 * - Sensor replacement workflow
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Settings,
  Trash2,
  RefreshCw,
  Search,
  MapPin,
  Calendar,
  Battery,
  Wifi,
  WifiOff,
  AlertTriangle,
  Wrench,
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { tempStickService } from '@/lib/tempstick-service';
import type { Sensor, StorageArea, TempStickSensor } from '@/types/tempstick';
import SensorErrorDisplay from './SensorErrorDisplay';
import type { SensorError } from './types';

interface SensorManagementProps {
  className?: string;
}

interface SensorFormData {
  name: string;
  location_description: string;
  storage_area_id: string | null;
  temp_min_threshold: number | null;
  temp_max_threshold: number | null;
  humidity_min_threshold: number | null;
  humidity_max_threshold: number | null;
  calibration_date: string | null;
  next_maintenance_due: string | null;
  maintenance_notes: string;
  is_active: boolean;
}

export const SensorManagement: React.FC<SensorManagementProps> = ({ className = '' }) => {
  // State management
  const [sensors, setSensors] = useState<Sensor[]>([]);
  const [storageAreas, setStorageAreas] = useState<StorageArea[]>([]);
  const [availableTempStickSensors, setAvailableTempStickSensors] = useState<TempStickSensor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<SensorError | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSensor, setSelectedSensor] = useState<Sensor | null>(null);
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  const [isDiscovering, setIsDiscovering] = useState(false);

  // Form state
  const [formData, setFormData] = useState<SensorFormData>({
    name: '',
    location_description: '',
    storage_area_id: null,
    temp_min_threshold: null,
    temp_max_threshold: null,
    humidity_min_threshold: null,
    humidity_max_threshold: null,
    calibration_date: null,
    next_maintenance_due: null,
    maintenance_notes: '',
    is_active: true,
  });

  /**
   * Load sensors and storage areas
   */
  const loadData = useCallback(async () => {
    try {
      setError(null);

      // Load sensors
      const { data: sensorsData, error: sensorsError } = await supabase
        .from('sensors')
        .select('*')
        .order('name');

      if (sensorsError) {
        throw new Error(`Failed to load sensors: ${sensorsError.message}`);
      }

      setSensors(sensorsData ?? []);

      // Load storage areas
      const { data: areasData, error: areasError } = await supabase
        .from('storage_areas')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (areasError) {
        throw new Error(`Failed to load storage areas: ${areasError.message}`);
      }

      setStorageAreas(areasData ?? []);
    } catch (err) {
      console.error('Failed to load sensor management data:', err);
      setError({
        id: crypto.randomUUID(),
        title: 'Failed to load sensor data',
        userMessage: 'We could not load sensor information right now.',
        actionSteps: [
          'Check your internet connection and retry.',
          'Verify your Supabase session is still active.',
          'Contact support if this keeps happening.',
        ],
        severity: 'error',
        source: 'supabase',
        retryable: true,
        occurredAt: new Date().toISOString(),
        operation: 'loadData',
        developerDetails: err instanceof Error ? err.stack ?? err.message : undefined,
      });
      // Reset dialog states on error to prevent UI issues
      setIsConfigDialogOpen(false);
      setSelectedSensor(null);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Discover available TempStick sensors
   */
  const discoverSensors = useCallback(async () => {
    setIsDiscovering(true);
    try {
      // Get sensors from TempStick API
      const tempStickSensors = await tempStickService.apiClient.getSensors();

      // Filter out sensors that are already registered
      const registeredSensorIds = new Set(sensors.map((s) => s.sensor_id));
      const availableSensors = tempStickSensors.filter(
        (sensor) => !registeredSensorIds.has(sensor.id)
      );

      setAvailableTempStickSensors(availableSensors);
    } catch (err) {
      console.error('Failed to discover sensors:', err);
      setError({
        id: crypto.randomUUID(),
        title: 'TempStick discovery failed',
        userMessage: 'We could not reach the TempStick API to list available sensors.',
        actionSteps: [
          'Confirm the TempStick API service is online.',
          'Retry discovery in a few moments.',
          'If the issue persists, check TempStick API credentials.',
        ],
        severity: 'warning',
        source: 'tempstick-api',
        retryable: true,
        occurredAt: new Date().toISOString(),
        operation: 'discoverSensors',
        developerDetails: err instanceof Error ? err.message : undefined,
      });
      // Reset dialog states on error
      setIsConfigDialogOpen(false);
      setSelectedSensor(null);
    } finally {
      setIsDiscovering(false);
    }
  }, [sensors]);

  /**
   * Register a new sensor from TempStick API
   */
  const registerSensor = useCallback(
    async (tempStickSensor: TempStickSensor) => {
      try {
        const newSensor = {
          sensor_id: tempStickSensor.id,
          name: tempStickSensor.name ?? `TempStick ${tempStickSensor.id}`,
          location: tempStickSensor.location ?? 'Unknown Location',
          is_online: tempStickSensor.status === 'online',
          battery_level: tempStickSensor.battery_level ?? null,
          is_active: true,
        };

        const { error } = await supabase.from('sensors').insert([newSensor]).select().single();

        if (error) {
          throw new Error(`Failed to register sensor: ${error.message}`);
        }

        // Refresh data
        await loadData();

        // Remove from available sensors
        setAvailableTempStickSensors((prev) => prev.filter((s) => s.id !== tempStickSensor.id));
      } catch (err) {
        console.error('Failed to register sensor:', err);
        setError({
          id: crypto.randomUUID(),
          title: 'Sensor registration failed',
          userMessage: 'Registering this TempStick sensor did not complete.',
          actionSteps: [
            'Ensure you have permission to add sensors.',
            'Retry registration in a few moments.',
            'Check Supabase logs for detailed errors.',
          ],
          severity: 'error',
          source: 'supabase',
          retryable: true,
          occurredAt: new Date().toISOString(),
          operation: 'registerSensor',
          developerDetails: err instanceof Error ? err.message : undefined,
        });
        // Reset dialog states on error
        setIsConfigDialogOpen(false);
        setSelectedSensor(null);
      }
    },
    [loadData]
  );

  /**
   * Open configuration dialog for sensor
   */
  const openConfigDialog = useCallback((sensor: Sensor) => {
    setSelectedSensor(sensor);
    setFormData({
      name: sensor.name,
      location_description: (sensor as { location_description?: string; location?: string }).location_description ?? sensor.location ?? '',
      storage_area_id: sensor.storage_area_id,
      temp_min_threshold: sensor.temp_min_threshold,
      temp_max_threshold: sensor.temp_max_threshold,
      humidity_min_threshold: sensor.humidity_min_threshold,
      humidity_max_threshold: sensor.humidity_max_threshold,
      calibration_date:
        (sensor as Sensor & { last_calibrated_at?: string }).last_calibrated_at ??
        sensor.calibration_date ??
        null,
      next_maintenance_due: sensor.next_maintenance_due,
      maintenance_notes: sensor.maintenance_notes ?? '',
      is_active: sensor.is_active,
    });
    setIsConfigDialogOpen(true);
  }, []);

  /**
   * Save sensor configuration
   */
  const saveSensorConfig = useCallback(async () => {
    if (!selectedSensor) return;

    try {
      const { error } = await supabase
        .from('sensors')
        .update({
          name: formData.name,
          location_description: formData.location_description,
          storage_area_id: formData.storage_area_id,
          temp_min_threshold: formData.temp_min_threshold,
          temp_max_threshold: formData.temp_max_threshold,
          humidity_min_threshold: formData.humidity_min_threshold,
          humidity_max_threshold: formData.humidity_max_threshold,
          // Map to schema column
          last_calibrated_at: formData.calibration_date,
          next_maintenance_due: formData.next_maintenance_due,
          maintenance_notes: formData.maintenance_notes,
          is_active: formData.is_active,
          updated_at: new Date().toISOString(),
        })
        .eq('id', selectedSensor.id);

      if (error) {
        throw new Error(`Failed to update sensor: ${error.message}`);
      }

      setIsConfigDialogOpen(false);
      setSelectedSensor(null);
      await loadData();
    } catch (err) {
      console.error('Failed to save sensor configuration:', err);
      setError({
        id: crypto.randomUUID(),
        title: 'Save configuration failed',
        userMessage: 'We could not update this sensor configuration.',
        actionSteps: [
          'Review your input and try saving again.',
          'Ensure your session is active and you have edit permissions.',
          'Contact support if the error continues.',
        ],
        severity: 'error',
        source: 'supabase',
        retryable: true,
        occurredAt: new Date().toISOString(),
        operation: 'saveSensorConfig',
        developerDetails: err instanceof Error ? err.message : undefined,
      });
    }
  }, [selectedSensor, formData, loadData]);

  /**
   * Delete sensor
   */
  const deleteSensor = useCallback(
    async (sensorId: string) => {
      if (!confirm('Are you sure you want to delete this sensor? This action cannot be undone.')) {
        return;
      }

      try {
        const { error } = await supabase.from('sensors').delete().eq('id', sensorId);

        if (error) {
          throw new Error(`Failed to delete sensor: ${error.message}`);
        }

        await loadData();
      } catch (err) {
        console.error('Failed to delete sensor:', err);
        setError({
          id: crypto.randomUUID(),
          title: 'Delete sensor failed',
          userMessage: 'We could not remove this sensor from the system.',
          actionSteps: [
            'Confirm you have permission to delete sensors.',
            'Retry deleting the sensor.',
            'Review Supabase logs for the precise error.',
          ],
          severity: 'error',
          source: 'supabase',
          retryable: false,
          occurredAt: new Date().toISOString(),
          operation: 'deleteSensor',
          developerDetails: err instanceof Error ? err.message : undefined,
        });
      }
    },
    [loadData]
  );

  /**
   * Schedule calibration for sensor
   */
  const _scheduleCalibration = useCallback(
    async (sensorId: string, date: string) => {
      try {
        const { error } = await supabase
          .from('sensors')
          .update({
            next_maintenance_due: date,
            updated_at: new Date().toISOString(),
          })
          .eq('id', sensorId);

        if (error) {
          throw new Error(`Failed to schedule calibration: ${error.message}`);
        }

        await loadData();
      } catch (err) {
        console.error('Failed to schedule calibration:', err);
        setError({
          id: crypto.randomUUID(),
          title: 'Calibration scheduling failed',
          userMessage: 'We could not schedule that calibration.',
          actionSteps: [
            'Pick a new date and try again.',
            'Confirm you have access to edit this sensor.',
            'Contact support with the calibration details.',
          ],
          severity: 'warning',
          source: 'supabase',
          retryable: true,
          occurredAt: new Date().toISOString(),
          operation: 'scheduleCalibration',
          developerDetails: err instanceof Error ? err.message : undefined,
        });
      }
    },
    [loadData]
  );

  /**
   * Filter sensors based on search term
   */
  const filteredSensors = sensors.filter(
    (sensor) =>
      sensor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      Boolean((sensor as { location_description?: string }).location_description?.toLowerCase().includes(searchTerm.toLowerCase())) ||
      sensor.sensor_id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  /**
   * Get sensor status styling
   */
  const getSensorStatusStyling = (sensor: Sensor) => {
    if (!sensor.is_active) {
      return { color: 'bg-gray-100 text-gray-800', icon: <WifiOff className="h-4 w-4" /> };
    }
    if (!sensor.is_online) {
      return { color: 'bg-red-100 text-red-800', icon: <WifiOff className="h-4 w-4" /> };
    }
    if (sensor.battery_level && sensor.battery_level < 25) {
      return { color: 'bg-yellow-100 text-yellow-800', icon: <Battery className="h-4 w-4" /> };
    }
    return { color: 'bg-green-100 text-green-800', icon: <Wifi className="h-4 w-4" /> };
  };

  // Initial data load
  useEffect(() => {
    loadData();
  }, [loadData]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Loading sensor management...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Sensor Management</h1>
          <p className="text-muted-foreground">Configure and manage temperature sensors</p>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={discoverSensors} disabled={isDiscovering}>
            <Search className={`h-4 w-4 mr-2 ${isDiscovering ? 'animate-spin' : ''}`} />
            Discover Sensors
          </Button>

          <Button onClick={loadData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <SensorErrorDisplay
          error={error}
          onRetry={() => {
            if (error.operation === 'discoverSensors') {
              void discoverSensors();
            } else if (error.operation === 'loadData') {
              void loadData();
            }
            setError(null);
          }}
          onDismiss={() => setError(null)}
        />
      )}

      {/* Search and Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search sensors by name, location, or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Available TempStick Sensors */}
      {availableTempStickSensors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Available TempStick Sensors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableTempStickSensors.map((sensor) => (
                <Card key={sensor.id} className="border-dashed">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{sensor.name ?? sensor.id}</h4>
                      <Badge variant="outline">
                        {sensor.status === 'online' ? 'Online' : 'Offline'}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {sensor.location ?? 'No location specified'}
                    </p>
                    <Button size="sm" onClick={() => registerSensor(sensor)} className="w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      Register Sensor
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Registered Sensors */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredSensors.map((sensor) => {
          const styling = getSensorStatusStyling(sensor);
          const storageArea = storageAreas.find((area) => area.id === sensor.storage_area_id);

          return (
            <Card key={sensor.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-sm font-medium truncate">{sensor.name}</CardTitle>
                    <p className="text-xs text-muted-foreground truncate">ID: {sensor.sensor_id}</p>
                  </div>
                  <Badge className={`${styling.color} flex items-center gap-1`}>
                    {styling.icon}
                    <span>{sensor.is_online ? 'Online' : 'Offline'}</span>
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="pt-0 space-y-3">
                {/* Location */}
                {sensor.location && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{sensor.location}</span>
                  </div>
                )}

                {/* Storage Area */}
                {storageArea && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="h-4 w-4 bg-blue-100 rounded flex-shrink-0" />
                    <span className="truncate">{storageArea.name}</span>
                  </div>
                )}

                {/* Battery Level */}
                {sensor.battery_level && (
                  <div className="flex items-center gap-2 text-sm">
                    <Battery className="h-4 w-4 text-muted-foreground" />
                    <span>{sensor.battery_level}%</span>
                  </div>
                )}

                {/* Last Calibration */}
                {((sensor as Sensor & { last_calibrated_at?: string }).last_calibrated_at ??
                  sensor.calibration_date) && (
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>
                      Calibrated:{' '}
                      {new Date(
                        String(
                          (sensor as Sensor & { last_calibrated_at?: string }).last_calibrated_at ??
                            sensor.calibration_date
                        )
                      ).toLocaleDateString()}
                    </span>
                  </div>
                )}

                {/* Maintenance Due */}
                {sensor.next_maintenance_due && (
                  <div className="flex items-center gap-2 text-sm text-orange-600">
                    <Wrench className="h-4 w-4" />
                    <span>
                      Maintenance due: {new Date(sensor.next_maintenance_due).toLocaleDateString()}
                    </span>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openConfigDialog(sensor)}
                    className="flex-1"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Configure
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteSensor(sensor.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredSensors.length === 0 && !loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <Settings className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No sensors found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm
                ? 'No sensors match your search criteria.'
                : 'No sensors have been registered yet.'}
            </p>
            <div className="flex gap-2 justify-center">
              {searchTerm && (
                <Button variant="outline" onClick={() => setSearchTerm('')}>
                  Clear Search
                </Button>
              )}
              <Button onClick={discoverSensors}>
                <Search className="h-4 w-4 mr-2" />
                Discover Sensors
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Configuration Dialog */}
      <Dialog open={isConfigDialogOpen} onOpenChange={setIsConfigDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Configure Sensor: {selectedSensor?.name}</DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="general" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="thresholds">Thresholds</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Sensor Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter sensor name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location_description">Location</Label>
                  <Input
                    id="location_description"
                    value={formData.location_description}
                    onChange={(e) => setFormData((prev) => ({ ...prev, location_description: e.target.value }))}
                    placeholder="Enter location description"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="storage-area">Storage Area</Label>
                <Select
                  value={formData.storage_area_id ?? 'none'}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      storage_area_id: value === 'none' ? null : value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select storage area" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No storage area</SelectItem>
                    {storageAreas.map((area) => (
                      <SelectItem key={area.id} value={area.id}>
                        {area.name} ({area.area_type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, is_active: checked }))
                  }
                />
                <Label htmlFor="active">Sensor is active</Label>
              </div>
            </TabsContent>

            <TabsContent value="thresholds" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="temp-min">Min Temperature (°F)</Label>
                  <Input
                    id="temp-min"
                    type="number"
                    value={formData.temp_min_threshold ?? ''}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        temp_min_threshold: e.target.value ? parseFloat(e.target.value) : null,
                      }))
                    }
                    placeholder="e.g., 32"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="temp-max">Max Temperature (°F)</Label>
                  <Input
                    id="temp-max"
                    type="number"
                    value={formData.temp_max_threshold ?? ''}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        temp_max_threshold: e.target.value ? parseFloat(e.target.value) : null,
                      }))
                    }
                    placeholder="e.g., 38"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="humidity-min">Min Humidity (%)</Label>
                  <Input
                    id="humidity-min"
                    type="number"
                    value={formData.humidity_min_threshold ?? ''}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        humidity_min_threshold: e.target.value ? parseFloat(e.target.value) : null,
                      }))
                    }
                    placeholder="e.g., 80"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="humidity-max">Max Humidity (%)</Label>
                  <Input
                    id="humidity-max"
                    type="number"
                    value={formData.humidity_max_threshold ?? ''}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        humidity_max_threshold: e.target.value ? parseFloat(e.target.value) : null,
                      }))
                    }
                    placeholder="e.g., 95"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="calibration-date">Last Calibration</Label>
                  <Input
                    id="calibration-date"
                    type="date"
                    value={formData.calibration_date ?? ''}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        calibration_date: e.target.value ?? null,
                      }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maintenance-due">Next Maintenance Due</Label>
                  <Input
                    id="maintenance-due"
                    type="date"
                    value={formData.next_maintenance_due ?? ''}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        next_maintenance_due: e.target.value ?? null,
                      }))
                    }
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maintenance-notes">Maintenance Notes</Label>
                <Textarea
                  id="maintenance-notes"
                  value={formData.maintenance_notes}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, maintenance_notes: e.target.value }))
                  }
                  placeholder="Enter maintenance notes, calibration details, etc."
                  rows={4}
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setIsConfigDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveSensorConfig}>Save Configuration</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SensorManagement;
