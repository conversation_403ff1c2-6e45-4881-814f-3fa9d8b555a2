/**
 * Progressive Disclosure Error Display Component
 *
 * Provides user-friendly error messaging with optional technical details:
 * - User-facing copy by default
 * - Expandable "Show technical details" section
 * - Recovery guidance with actionable steps
 * - Retry/dismiss controls
 * - Accessible and responsive design
 */

import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import {
  AlertTriangle,
  XCircle,
  AlertCircle,
  Info,
  ChevronDown,
  ChevronRight,
  RefreshCw,
  X,
  CheckCircle,
  ExternalLink,
} from 'lucide-react';
import type { SensorError } from './types';

interface SensorErrorDisplayProps {
  error: SensorError;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
}

export const SensorErrorDisplay: React.FC<SensorErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  className = '',
}) => {
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false);

  /**
   * Get error icon and styling based on severity
   */
  const getErrorStyling = () => {
    switch (error.severity) {
      case 'critical':
        return {
          icon: <XCircle className="h-5 w-5" />,
          color: 'destructive',
          bgColor: 'bg-red-50 border-red-200',
          textColor: 'text-red-800',
          badgeVariant: 'destructive' as const,
        };
      case 'error':
        return {
          icon: <AlertTriangle className="h-5 w-5" />,
          color: 'destructive',
          bgColor: 'bg-red-50 border-red-200',
          textColor: 'text-red-700',
          badgeVariant: 'destructive' as const,
        };
      case 'warning':
        return {
          icon: <AlertCircle className="h-5 w-5" />,
          color: 'default',
          bgColor: 'bg-yellow-50 border-yellow-200',
          textColor: 'text-yellow-800',
          badgeVariant: 'secondary' as const,
        };
      case 'info':
        return {
          icon: <Info className="h-5 w-5" />,
          color: 'default',
          bgColor: 'bg-blue-50 border-blue-200',
          textColor: 'text-blue-800',
          badgeVariant: 'secondary' as const,
        };
      default:
        return {
          icon: <AlertTriangle className="h-5 w-5" />,
          color: 'default',
          bgColor: 'bg-gray-50 border-gray-200',
          textColor: 'text-gray-800',
          badgeVariant: 'secondary' as const,
        };
    }
  };

  const styling = getErrorStyling();

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  /**
   * Get source display name
   */
  const getSourceDisplayName = (source: string) => {
    switch (source) {
      case 'tempstick-api':
        return 'TempStick API';
      case 'supabase':
        return 'Database';
      case 'ui':
        return 'User Interface';
      default:
        return 'Unknown';
    }
  };

  return (
    <Card className={`${styling.bgColor} ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className={styling.textColor}>{styling.icon}</div>
            <div className="flex-1 min-w-0">
              <CardTitle className={`text-base font-medium ${styling.textColor}`}>
                {error.title}
              </CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={styling.badgeVariant} className="text-xs">
                  {error.severity.toUpperCase()}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {getSourceDisplayName(error.source)}
                </span>
                <span className="text-xs text-muted-foreground">
                  {formatTimestamp(error.occurredAt)}
                </span>
              </div>
            </div>
          </div>

          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className={`${styling.textColor} hover:bg-white/50`}
              aria-label="Dismiss error"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-4">
        {/* User-friendly message */}
        <p className={`text-sm ${styling.textColor}`}>{error.userMessage}</p>

        {/* Recovery guidance */}
        {error.actionSteps && error.actionSteps.length > 0 && (
          <div className="space-y-2">
            <h4 className={`text-sm font-medium ${styling.textColor}`}>What you can do:</h4>
            <ul className="space-y-1">
              {error.actionSteps.map((step, index) => (
                <li key={index} className="flex items-start space-x-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className={styling.textColor}>{step}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Support link */}
        {error.supportUrl && (
          <div>
            <Button variant="outline" size="sm" asChild className="text-xs">
              <a
                href={error.supportUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-1"
              >
                <ExternalLink className="h-3 w-3" />
                <span>Get help</span>
              </a>
            </Button>
          </div>
        )}

        {/* Technical details (collapsible) */}
        <div className="space-y-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
            className={`${styling.textColor} hover:bg-white/50 p-0 h-auto font-normal text-xs`}
            aria-expanded={showTechnicalDetails}
            aria-controls="technical-details"
          >
            {showTechnicalDetails ? (
              <ChevronDown className="h-3 w-3 mr-1" />
            ) : (
              <ChevronRight className="h-3 w-3 mr-1" />
            )}
            {showTechnicalDetails ? 'Hide technical details' : 'Show technical details'}
          </Button>
          
          {showTechnicalDetails && (
            <div
              id="technical-details"
              className="mt-2 space-y-2 text-xs font-mono bg-white/50 rounded p-3 border"
            >
              <div className="space-y-1">
                <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-gray-600">Error ID:</span>
                  <span className="col-span-2 break-all">{error.id}</span>
                </div>

                {error.operation && (
                  <div className="grid grid-cols-3 gap-2">
                    <span className="font-semibold text-gray-600">Operation:</span>
                    <span className="col-span-2">{error.operation}</span>
                  </div>
                )}

                {error.correlationId && (
                  <div className="grid grid-cols-3 gap-2">
                    <span className="font-semibold text-gray-600">Correlation ID:</span>
                    <span className="col-span-2 break-all">{error.correlationId}</span>
                  </div>
                )}

                {error.statusCode && (
                  <div className="grid grid-cols-3 gap-2">
                    <span className="font-semibold text-gray-600">Status Code:</span>
                    <span className="col-span-2">{error.statusCode}</span>
                  </div>
                )}

                <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-gray-600">Source:</span>
                  <span className="col-span-2">{error.source}</span>
                </div>

                <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-gray-600">Retryable:</span>
                  <span className="col-span-2">{error.retryable ? 'Yes' : 'No'}</span>
                </div>

                <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-gray-600">Timestamp:</span>
                  <span className="col-span-2">{error.occurredAt}</span>
                </div>

                {error.developerDetails && (
                  <div className="space-y-1">
                    <span className="font-semibold text-gray-600">Developer Details:</span>
                    <pre className="whitespace-pre-wrap break-words text-xs bg-gray-100 p-2 rounded">
                      {error.developerDetails}
                    </pre>
                  </div>
                )}

                {error.metadata && Object.keys(error.metadata).length > 0 && (
                  <div className="space-y-1">
                    <span className="font-semibold text-gray-600">Metadata:</span>
                    <pre className="whitespace-pre-wrap break-words text-xs bg-gray-100 p-2 rounded">
                      {JSON.stringify(error.metadata, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex items-center space-x-2 pt-2">
          {onRetry && error.retryable && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="inline-flex items-center space-x-1"
            >
              <RefreshCw className="h-3 w-3" />
              <span>Retry</span>
            </Button>
          )}

          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="text-xs"
            >
              Dismiss
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SensorErrorDisplay;