import { clsx } from 'clsx';
import { useResilienceState } from '@/hooks/useResilienceState';

type DegradedModeIndicatorProps = {
  compact?: boolean;
  className?: string;
  service?: 'tempstick' | 'voice' | 'database';
  onRetry?: () => void;
};

const statusCopy: Record<string, { title: string; description: string }> = {
  healthy: {
    title: 'All systems operational',
    description: 'Full functionality is available.',
  },
  degraded: {
    title: 'Operating in degraded mode',
    description: 'Some features may be limited while we recover.',
  },
  offline: {
    title: 'Offline mode',
    description: 'Live data unavailable. Showing cached information when possible.',
  },
};

const modeCopy: Record<string, string> = {
  full_functionality: 'Full functionality available',
  limited_functionality: 'Limited capability mode activated',
  offline_mode: 'Offline mode active — working from cached data',
  emergency_mode: 'Emergency fallback mode — critical operations only',
};

const statusColor: Record<string, string> = {
  healthy: 'bg-emerald-500/90',
  degraded: 'bg-amber-500/90 animate-pulse',
  offline: 'bg-rose-500/90 animate-pulse',
};

export function DegradedModeIndicator({ compact = false, className, service, onRetry }: DegradedModeIndicatorProps) {
  const resilience = useResilienceState();
  const serviceState = service ? resilience.services[service] : undefined;
  const statusKey = serviceState?.status ?? resilience.mode === 'full_functionality' ? 'healthy' : 'degraded';
  const badgeLabel = service ? `${service.toUpperCase()} ${statusCopy[statusKey]?.title ?? ''}` : modeCopy[resilience.mode];

  if (compact) {
    return (
      <div
        className={clsx(
          'inline-flex items-center gap-2 rounded-full px-3 py-1 text-sm font-medium',
          statusColor[statusKey] ?? 'bg-slate-500/70',
          className
        )}
        role="status"
        aria-live="polite"
      >
        <span className="inline-flex h-2.5 w-2.5 rounded-full bg-white" aria-hidden />
        <span>{badgeLabel}</span>
        {onRetry ? (
          <button
            type="button"
            onClick={onRetry}
            className="ml-2 rounded-full bg-white/20 px-2 py-0.5 text-xs font-semibold text-white transition hover:bg-white/30 focus:outline-none focus:ring-2 focus:ring-white"
          >
            Retry
          </button>
        ) : null}
      </div>
    );
  }

  const content = statusCopy[statusKey] ?? statusCopy.degraded;

  return (
    <section
      className={clsx(
        'rounded-lg border border-white/10 bg-slate-900/70 p-4 text-sm text-white shadow-inner backdrop-blur',
        className
      )}
      role="status"
      aria-live="polite"
    >
      <header className="flex items-center justify-between gap-3">
        <div className="flex items-center gap-2">
          <span className={clsx('inline-flex h-2.5 w-2.5 rounded-full', statusColor[statusKey] ?? 'bg-slate-500/70')} aria-hidden />
          <h3 className="text-base font-semibold">{content.title}</h3>
        </div>
        {onRetry ? (
          <button
            type="button"
            onClick={onRetry}
            className="rounded-md border border-white/20 px-3 py-1 text-xs font-semibold uppercase tracking-wide text-white transition hover:border-white/40 focus:outline-none focus:ring-2 focus:ring-white"
          >
            Retry Now
          </button>
        ) : null}
      </header>

      <p className="mt-2 text-slate-200/80">{content.description}</p>

      <dl className="mt-4 grid gap-3 sm:grid-cols-2">
        <div>
          <dt className="text-xs uppercase tracking-wide text-slate-300/60">Current mode</dt>
          <dd className="text-sm font-medium text-white">{modeCopy[resilience.mode]}</dd>
        </div>
        {serviceState ? (
          <div>
            <dt className="text-xs uppercase tracking-wide text-slate-300/60">Service status</dt>
            <dd className="text-sm font-medium text-white">
              {statusCopy[serviceState.status]?.title ?? 'Status unknown'}
            </dd>
          </div>
        ) : null}
        {resilience.degradedCapabilities.length ? (
          <div className="sm:col-span-2">
            <dt className="text-xs uppercase tracking-wide text-slate-300/60">Available capabilities</dt>
            <dd className="mt-1 flex flex-wrap gap-2">
              {resilience.degradedCapabilities.map((capability) => (
                <span
                  key={capability}
                  className="rounded-full bg-white/10 px-3 py-1 text-xs font-semibold text-white/90"
                >
                  {capability}
                </span>
              ))}
            </dd>
          </div>
        ) : null}
        {serviceState?.reason ? (
          <div className="sm:col-span-2">
            <dt className="text-xs uppercase tracking-wide text-slate-300/60">Details</dt>
            <dd className="mt-1 rounded-md bg-white/5 p-2 text-sm text-white/80">
              {serviceState.reason}
            </dd>
          </div>
        ) : null}
        {resilience.offlineStorage.persistentSupported ? (
          <div>
            <dt className="text-xs uppercase tracking-wide text-slate-300/60">Offline storage</dt>
            <dd className="text-sm text-white/80">Enabled</dd>
          </div>
        ) : (
          <div>
            <dt className="text-xs uppercase tracking-wide text-slate-300/60">Offline storage</dt>
            <dd className="text-sm text-white/80">Unavailable</dd>
          </div>
        )}
      </dl>
    </section>
  );
}
