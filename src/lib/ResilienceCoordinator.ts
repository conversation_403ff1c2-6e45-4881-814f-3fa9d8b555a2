import { offlineStorageManager, type OfflineStorageStatus } from './OfflineStorageManager';
import { errorRecoveryManager } from '@/services/voice-agent/ErrorRecoveryManager';

export type ResilienceMode =
  | 'full_functionality'
  | 'limited_functionality'
  | 'offline_mode'
  | 'emergency_mode';

type ServiceKey = 'tempstick' | 'voice' | 'database';

export type ServiceStatus = {
  status: 'healthy' | 'degraded' | 'offline';
  reason?: string;
  lastUpdated: number;
  circuitOpen?: boolean;
  failures?: number;
  recoveryEtaMs?: number;
};

export type ResilienceState = {
  mode: ResilienceMode;
  services: Record<ServiceKey, ServiceStatus>;
  offlineStorage: OfflineStorageStatus;
  degradedCapabilities: string[];
  updatedAt: number;
};

type ResilienceListener = (state: ResilienceState) => void;

const defaultServiceState: ServiceStatus = {
  status: 'healthy',
  lastUpdated: Date.now(),
};

class ResilienceCoordinator {
  private static instance: ResilienceCoordinator | null = null;

  private readonly listeners = new Set<ResilienceListener>();
  private readonly serviceState: Record<ServiceKey, ServiceStatus> = {
    tempstick: { ...defaultServiceState },
    voice: { ...defaultServiceState },
    database: { ...defaultServiceState },
  };
  private mode: ResilienceMode = 'full_functionality';
  private degradedCapabilities: string[] = [];
  private offlineStatus: OfflineStorageStatus = { persistentSupported: false };

  private constructor() {
    void this.refreshOfflineStatus();
  }

  static getInstance(): ResilienceCoordinator {
    this.instance ??= new ResilienceCoordinator();
    return this.instance;
  }

  getState(): ResilienceState {
    return {
      mode: this.mode,
      services: { ...this.serviceState },
      offlineStorage: { ...this.offlineStatus },
      degradedCapabilities: [...this.degradedCapabilities],
      updatedAt: Date.now(),
    };
  }

  subscribe(listener: ResilienceListener): () => void {
    this.listeners.add(listener);
    listener(this.getState());
    return () => {
      this.listeners.delete(listener);
    };
  }

  updateServiceStatus(service: ServiceKey, status: Partial<ServiceStatus>): void {
    const existing = this.serviceState[service];
    this.serviceState[service] = {
      ...existing,
      ...status,
      lastUpdated: Date.now(),
    };
    this.recalculateMode();
    this.emit();
  }

  setDegradedCapabilities(capabilities: string[]): void {
    this.degradedCapabilities = capabilities;
    this.recalculateMode();
    this.emit();
  }

  signalCircuitBreaker(service: ServiceKey, isOpen: boolean, failures?: number): void {
    this.updateServiceStatus(service, {
      circuitOpen: isOpen,
      failures,
      status: isOpen ? 'offline' : this.serviceState[service].status,
      reason: isOpen ? 'Circuit breaker open' : undefined,
    });
  }

  async refreshOfflineStatus(): Promise<void> {
    const offlineStatus = await offlineStorageManager.getStatus();
    this.offlineStatus = { ...offlineStatus };
    this.emit();
  }

  private recalculateMode(): void {
    const services = Object.values(this.serviceState);
    if (services.some((s) => s.status === 'offline')) {
      this.mode = 'offline_mode';
      return;
    }
    if (services.some((s) => s.status === 'degraded') || this.degradedCapabilities.length > 0) {
      this.mode = 'limited_functionality';
      return;
    }
    this.mode = 'full_functionality';
  }

  private emit(): void {
    const snapshot = this.getState();
    this.listeners.forEach((listener) => {
      try {
        listener(snapshot);
      } catch (error) {
        console.warn('[ResilienceCoordinator] Listener error', error);
      }
    });
  }

  integrateErrorStatistics(): void {
    const stats = errorRecoveryManager.getErrorStatistics();
    stats.circuitBreakers.forEach((breaker) => {
      if (breaker.isOpen) {
        this.signalCircuitBreaker('voice', true, breaker.failures);
      }
    });
  }
}

export const resilienceCoordinator = ResilienceCoordinator.getInstance();
