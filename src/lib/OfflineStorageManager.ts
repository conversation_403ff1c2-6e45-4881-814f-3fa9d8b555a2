import type { TempStickReading, TempStickSensor } from '@/types/tempstick';

type SensorKey = 'tempstick:sensors';
type AlertsKey = 'tempstick:alerts';
type NotificationsKey = 'tempstick:notifications';
type ReadingsKey = `tempstick:readings:${string}`;
type SyncQueueKey = `queue:sync:${string}`;

type StorageKey = SensorKey | AlertsKey | NotificationsKey | ReadingsKey | SyncQueueKey;

type StoragePayload<T> = {
  data: T;
  storedAt: number;
  expiresAt?: number;
};

export type OfflineStorageStatus = {
  persistentSupported: boolean;
  quotaBytes?: number;
  usageBytes?: number;
  lastCleanup?: number;
};

type ReadingBucket = {
  sensorId: string;
  readings: TempStickReading[];
  storedAt: number;
};

type SyncQueueItem = {
  id: string;
  type: 'reading' | 'alert' | 'notification' | 'custom';
  payload: unknown;
  createdAt: number;
  correlationId?: string;
};

export type OfflineDataFreshness = {
  stale: boolean;
  ageMs: number;
  expiresInMs?: number;
};

type Listener<T> = (value: T) => void;

const SENSOR_KEY: SensorKey = 'tempstick:sensors';
const ALERTS_KEY: AlertsKey = 'tempstick:alerts';
const NOTIFICATIONS_KEY: NotificationsKey = 'tempstick:notifications';

type OfflineStorageConfig = {
  sensorTtlMs: number;
  readingsTtlMs: number;
  alertsTtlMs: number;
  syncQueueKey: SyncQueueKey;
};

const defaultConfig: OfflineStorageConfig = {
  sensorTtlMs: 5 * 60 * 1000,
  readingsTtlMs: 15 * 60 * 1000,
  alertsTtlMs: 10 * 60 * 1000,
  syncQueueKey: 'queue:sync:global',
};

const isBrowser = typeof window !== 'undefined';

class StorageDriver {
  private dbPromise: Promise<IDBDatabase | null> | null = null;

  async isIndexedDbSupported(): Promise<boolean> {
    if (!isBrowser || typeof indexedDB === 'undefined') {
      return false;
    }
    try {
      const db = await this.openDb();
      if (db) {
        db.close();
      }
      return Boolean(db);
    } catch {
      return false;
    }
  }

  private openDb(): Promise<IDBDatabase | null> {
    if (!isBrowser || typeof indexedDB === 'undefined') {
      return Promise.resolve(null);
    }
    if (this.dbPromise) {
      return this.dbPromise;
    }

    this.dbPromise = new Promise<IDBDatabase | null>((resolve, reject) => {
      const request = indexedDB.open('seafood-manager-offline', 1);

      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('records')) {
          db.createObjectStore('records');
        }
      };

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        reject(request.error ?? new Error('Failed to open offline storage database'));
      };
    });

    return this.dbPromise;
  }

  async getItem<T>(key: StorageKey): Promise<StoragePayload<T> | null> {
    const db = await this.openDb();
    if (!db) {
      return this.getFromLocalStorage<T>(key);
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction('records', 'readonly');
      const store = transaction.objectStore('records');
      const request = store.get(key);

      request.onsuccess = () => {
        resolve((request.result as StoragePayload<T> | undefined) ?? null);
      };
      request.onerror = () => {
        reject(request.error ?? new Error('Failed to read offline storage entry'));
      };
    });
  }

  async setItem<T>(key: StorageKey, payload: StoragePayload<T>): Promise<void> {
    const db = await this.openDb();
    if (!db) {
      this.setToLocalStorage(key, payload);
      return;
    }

    await new Promise<void>((resolve, reject) => {
      const transaction = db.transaction('records', 'readwrite');
      const store = transaction.objectStore('records');
      const request = store.put(payload, key);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error ?? new Error('Failed to write offline storage entry'));
    });
  }

  async removeItem(key: StorageKey): Promise<void> {
    const db = await this.openDb();
    if (!db) {
      if (isBrowser) {
        window.localStorage.removeItem(key);
      }
      return;
    }

    await new Promise<void>((resolve, reject) => {
      const transaction = db.transaction('records', 'readwrite');
      const store = transaction.objectStore('records');
      const request = store.delete(key);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error ?? new Error('Failed to remove offline storage entry'));
    });
  }

  private getFromLocalStorage<T>(key: StorageKey): StoragePayload<T> | null {
    if (!isBrowser) {
      return null;
    }
    try {
      const raw = window.localStorage.getItem(key);
      if (!raw) {
        return null;
      }
      return JSON.parse(raw) as StoragePayload<T>;
    } catch {
      window.localStorage.removeItem(key);
      return null;
    }
  }

  private setToLocalStorage<T>(key: StorageKey, payload: StoragePayload<T>): void {
    if (!isBrowser) {
      return;
    }
    try {
      window.localStorage.setItem(key, JSON.stringify(payload));
    } catch (error) {
      console.warn('[OfflineStorage] Failed to persist entry to localStorage', error);
    }
  }
}

export class OfflineStorageManager {
  private static instance: OfflineStorageManager | null = null;
  private readonly driver = new StorageDriver();
  private status: OfflineStorageStatus = { persistentSupported: false };
  private listeners = new Map<string, Set<Listener<unknown>>>();
  private cleanupTimer: ReturnType<typeof setTimeout> | null = null;
  private readonly readingKeys = new Set<ReadingsKey>();

  private constructor(private readonly config: OfflineStorageConfig = defaultConfig) {
    void this.initialize();
  }

  static getInstance(): OfflineStorageManager {
    this.instance ??= new OfflineStorageManager();
    return this.instance;
  }

  async initialize(): Promise<void> {
    const persistentSupported = await this.driver.isIndexedDbSupported();
    this.status = {
      ...this.status,
      persistentSupported,
    };
    if (persistentSupported) {
      await this.updateQuotaUsage();
    }
    this.scheduleCleanup();
  }

  private async updateQuotaUsage(): Promise<void> {
    if (!isBrowser || !navigator.storage?.estimate) {
      return;
    }
    try {
      const estimate = await navigator.storage.estimate();
      this.status = {
        ...this.status,
        quotaBytes: estimate.quota ?? undefined,
        usageBytes: estimate.usage ?? undefined,
      };
    } catch (error) {
      console.warn('[OfflineStorage] Unable to estimate storage usage', error);
    }
  }

  private scheduleCleanup(): void {
    if (this.cleanupTimer) {
      clearTimeout(this.cleanupTimer);
    }
    this.cleanupTimer = setTimeout(() => {
      void this.cleanupExpiredEntries();
    }, 60_000);
  }

  async getStatus(): Promise<OfflineStorageStatus> {
    await this.updateQuotaUsage();
    return { ...this.status };
  }

  async storeSensorData(sensors: TempStickSensor[], timestamp = Date.now()): Promise<void> {
    const payload: StoragePayload<TempStickSensor[]> = {
      data: sensors,
      storedAt: timestamp,
      expiresAt: timestamp + this.config.sensorTtlMs,
    };
    await this.driver.setItem(SENSOR_KEY, payload);
    this.emit(SENSOR_KEY, payload);
  }

  async getStoredSensorData(maxAgeMs = this.config.sensorTtlMs): Promise<{
    sensors: TempStickSensor[];
    freshness: OfflineDataFreshness;
  } | null> {
    const payload = await this.driver.getItem<TempStickSensor[]>(SENSOR_KEY);
    if (!payload) {
      return null;
    }
    if (this.isExpired(payload, maxAgeMs)) {
      await this.driver.removeItem(SENSOR_KEY);
      return null;
    }
    return {
      sensors: payload.data,
      freshness: this.getFreshness(payload, maxAgeMs),
    };
  }

  async storeReadings(sensorId: string, readings: TempStickReading[], timestamp = Date.now()): Promise<void> {
    const key: ReadingsKey = `tempstick:readings:${sensorId}`;
    const payload: StoragePayload<ReadingBucket> = {
      data: {
        sensorId,
        readings,
        storedAt: timestamp,
      },
      storedAt: timestamp,
      expiresAt: timestamp + this.config.readingsTtlMs,
    };
    await this.driver.setItem(key, payload);
    this.readingKeys.add(key);
    this.emit(key, payload);
  }

  async getStoredReadings(sensorId: string, _dateRange?: { start: Date; end: Date }): Promise<{
    readings: TempStickReading[];
    freshness: OfflineDataFreshness;
  } | null> {
    const key: ReadingsKey = `tempstick:readings:${sensorId}`;
    const payload = await this.driver.getItem<ReadingBucket>(key);
    if (!payload) {
      this.readingKeys.delete(key);
      return null;
    }
    if (this.isExpired(payload, this.config.readingsTtlMs)) {
      await this.driver.removeItem(key);
      this.readingKeys.delete(key);
      return null;
    }
    return {
      readings: payload.data.readings,
      freshness: this.getFreshness(payload, this.config.readingsTtlMs),
    };
  }

  async storeAlerts(alerts: unknown[], timestamp = Date.now()): Promise<void> {
    const payload: StoragePayload<unknown[]> = {
      data: alerts,
      storedAt: timestamp,
      expiresAt: timestamp + this.config.alertsTtlMs,
    };
    await this.driver.setItem(ALERTS_KEY, payload);
    this.emit(ALERTS_KEY, payload);
  }

  async getStoredAlerts(): Promise<{
    alerts: unknown[];
    freshness: OfflineDataFreshness;
  } | null> {
    const payload = await this.driver.getItem<unknown[]>(ALERTS_KEY);
    if (!payload || this.isExpired(payload, this.config.alertsTtlMs)) {
      return null;
    }
    return {
      alerts: payload.data,
      freshness: this.getFreshness(payload, this.config.alertsTtlMs),
    };
  }

  async storeNotifications(notifications: unknown[], timestamp = Date.now()): Promise<void> {
    const payload: StoragePayload<unknown[]> = {
      data: notifications,
      storedAt: timestamp,
      expiresAt: timestamp + this.config.alertsTtlMs,
    };
    await this.driver.setItem(NOTIFICATIONS_KEY, payload);
    this.emit(NOTIFICATIONS_KEY, payload);
  }

  async getStoredNotifications(): Promise<{
    notifications: unknown[];
    freshness: OfflineDataFreshness;
  } | null> {
    const payload = await this.driver.getItem<unknown[]>(NOTIFICATIONS_KEY);
    if (!payload || this.isExpired(payload, this.config.alertsTtlMs)) {
      return null;
    }
    return {
      notifications: payload.data,
      freshness: this.getFreshness(payload, this.config.alertsTtlMs),
    };
  }

  async enqueueSync(item: SyncQueueItem): Promise<void> {
    const key = this.config.syncQueueKey;
    const payload = (await this.driver.getItem<SyncQueueItem[]>(key)) ?? {
      data: [],
      storedAt: Date.now(),
    };
    payload.data.push(item);
    payload.storedAt = Date.now();
    await this.driver.setItem(key, payload);
    this.emit(key, payload);
  }

  async getQueuedSyncItems(): Promise<SyncQueueItem[]> {
    const payload = await this.driver.getItem<SyncQueueItem[]>(this.config.syncQueueKey);
    if (!payload) {
      return [];
    }
    return payload.data;
  }

  async clearQueuedSyncItems(predicate?: (item: SyncQueueItem) => boolean): Promise<void> {
    const key = this.config.syncQueueKey;
    const payload = await this.driver.getItem<SyncQueueItem[]>(key);
    if (!payload) {
      return;
    }
    const filtered = predicate ? payload.data.filter((item) => !predicate(item)) : [];
    const updatedPayload: StoragePayload<SyncQueueItem[]> = {
      data: filtered,
      storedAt: Date.now(),
    };
    await this.driver.setItem(key, updatedPayload);
    this.emit(key, updatedPayload);
  }

  subscribe<T>(key: StorageKey, listener: Listener<T>): () => void {
    const set = this.listeners.get(key) ?? new Set();
    set.add(listener as Listener<unknown>);
    this.listeners.set(key, set);
    return () => {
      set.delete(listener as Listener<unknown>);
      if (set.size === 0) {
        this.listeners.delete(key);
      }
    };
  }

  private emit<T>(key: StorageKey, payload: T): void {
    const listeners = this.listeners.get(key);
    if (!listeners) {
      return;
    }
    listeners.forEach((listener) => {
      try {
        (listener as Listener<T>)(payload);
      } catch (error) {
        console.warn('[OfflineStorage] Listener error', error);
      }
    });
  }

  private isExpired<T>(payload: StoragePayload<T>, maxAgeMs: number): boolean {
    const now = Date.now();
    if (payload.expiresAt && payload.expiresAt < now) {
      return true;
    }
    if (now - payload.storedAt > maxAgeMs) {
      return true;
    }
    return false;
  }

  private getFreshness<T>(payload: StoragePayload<T>, defaultTtl: number): OfflineDataFreshness {
    const now = Date.now();
    const ageMs = now - payload.storedAt;
    const expiresIn = payload.expiresAt ? payload.expiresAt - now : defaultTtl - ageMs;
    return {
      stale: ageMs > defaultTtl,
      ageMs,
      expiresInMs: expiresIn > 0 ? expiresIn : undefined,
    };
  }

  private async cleanupExpiredEntries(): Promise<void> {
    const staticKeys: StorageKey[] = [SENSOR_KEY, ALERTS_KEY, NOTIFICATIONS_KEY, this.config.syncQueueKey];
    const readingKeys = Array.from(this.readingKeys);
    await Promise.all(
      [...staticKeys, ...readingKeys].map(async (key) => {
        const payload = await this.driver.getItem<unknown>(key);
        if (!payload) {
          if (key.startsWith('tempstick:readings:')) {
            this.readingKeys.delete(key as ReadingsKey);
          }
          return;
        }
        const ttl = key.startsWith('tempstick:readings:')
          ? this.config.readingsTtlMs
          : key === SENSOR_KEY
            ? this.config.sensorTtlMs
            : this.config.alertsTtlMs;
        if (this.isExpired(payload as StoragePayload<unknown>, ttl)) {
          await this.driver.removeItem(key);
          if (key.startsWith('tempstick:readings:')) {
            this.readingKeys.delete(key as ReadingsKey);
          }
        }
      })
    );
    this.status = {
      ...this.status,
      lastCleanup: Date.now(),
    };
    this.scheduleCleanup();
  }
}

export const offlineStorageManager = OfflineStorageManager.getInstance();
