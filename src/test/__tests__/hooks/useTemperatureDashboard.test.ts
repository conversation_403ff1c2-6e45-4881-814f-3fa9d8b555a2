import { act, renderHook, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useTemperatureDashboard } from '@/hooks/useTemperatureDashboard';
import { supabase } from '@/lib/supabase';

vi.mock('@/hooks/useSensorStatuses', () => ({
  __esModule: true,
  default: vi.fn(() => ({ refresh: vi.fn() })),
}));

vi.mock('@/lib/supabase', () => {
  const from = vi.fn();
  const channel = vi.fn();
  const removeChannel = vi.fn();

  return {
    supabase: {
      from,
      channel,
      removeChannel,
    },
  };
});

const mockSupabase = vi.mocked(supabase);

const createBuilder = (table: string, dataMap: Record<string, any[]>) => {
  const builder: any = {
    _table: table,
    _data: dataMap[table] ?? [],
    _error: null,
    select: vi.fn(() => builder),
    eq: vi.fn(() => builder),
    in: vi.fn(() => builder),
    order: vi.fn(() => builder),
    limit: vi.fn(() => builder),
    then: (resolve: (value: { data: unknown[]; error: null }) => void) =>
      resolve({ data: builder._data, error: builder._error }),
  };
  return builder;
};

describe('useTemperatureDashboard hook (Supabase integration)', () => {
  const sensors = [
    {
      id: 'internal-1',
      sensor_id: 'sensor-1',
      name: 'Freezer Sensor',
      location_description: 'Downstairs Freezer',
      sensor_type: 'temperature_humidity',
      custom_temp_min_celsius: 30,
      custom_temp_max_celsius: 33,
      custom_humidity_min: null,
      custom_humidity_max: null,
      storage_area_id: 'storage-1',
      is_active: true,
      is_online: true,
      battery_level: 80,
      signal_strength: -40,
      last_seen_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
    },
  ];

  const readings = [
    {
      id: 'reading-1',
      sensor_id: 'internal-1',
      temp_celsius: 35,
      humidity: 55,
      recorded_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      alert_triggered: true,
    },
  ];

  const storageAreas = [{ id: 'storage-1', name: 'Downstairs Freezer', description: null }];

  const trends = [
    {
      sensor_id: 'internal-1',
      sensor_external_id: 'sensor-1',
      sensor_name: 'Freezer Sensor',
      temp_celsius: 35,
      recorded_at: new Date().toISOString(),
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    const dataMap: Record<string, any[]> = {
      sensors,
      temperature_readings: readings,
      storage_areas: storageAreas,
      temperature_readings_with_sensors: trends,
    };

    mockSupabase.from.mockImplementation((table: string) => createBuilder(table, dataMap) as any);
    mockSupabase.channel.mockReturnValue({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn().mockReturnValue({ unsubscribe: vi.fn() }),
    } as any);
  });

  it('loads sensors, summary, and trends from Supabase', async () => {
    const { result } = renderHook(() => useTemperatureDashboard());

    await waitFor(() => expect(result.current.loading).toBe(false));

    expect(result.current.sensorStatuses).toHaveLength(1);
    expect(result.current.dashboardSummary).toMatchObject({
      totalSensors: 1,
      onlineSensors: 1,
      alertCount: 1,
    });
    expect(result.current.availableStorageAreas).toHaveLength(1);

    await act(async () => {
      await result.current.fetchTemperatureTrends();
    });

    expect(result.current.temperatureTrends).toHaveLength(1);
  });

  it('surface errors from Supabase queries', async () => {
    mockSupabase.from.mockReset();
    mockSupabase.from
      .mockImplementationOnce(() => ({
        select: vi.fn(async () => ({ data: null, error: { message: 'boom' } })),
        order: vi.fn().mockReturnThis(),
        in: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
      }) as any)
      .mockImplementation((table: string) =>
        createBuilder(table, {
          sensors,
          temperature_readings: readings,
          storage_areas: storageAreas,
          temperature_readings_with_sensors: trends,
        }) as any
      );

    const { result } = renderHook(() => useTemperatureDashboard());

    await waitFor(() => expect(result.current.loading).toBe(false));
    expect(result.current.error).toBeDefined();
  });
});
