import type { SupabaseClient } from '@supabase/supabase-js';

import { supabaseService } from '@/lib/supabase-service';
import { cToF } from '@/lib/units';
import type {
  LotAgingAlert,
  LotStorageDetail,
  StorageAreaTemperatureSummary,
  TemperatureViolationImpact,
} from '@/services/voice-agent/functions/types';

const WARNING_THRESHOLD_DAYS = 150;
const CRITICAL_THRESHOLD_DAYS = 180;

export interface TemperatureLotQueryOptions {
  search?: string;
  storageTypes?: string[];
  unit?: 'F' | 'C';
  includeLotDetails?: boolean;
  includeAging?: boolean;
  includeViolations?: boolean;
  minimumFreezerTempCelsius?: number;
}

export interface StorageAreaLotSummaryOptions {
  storageAreaName?: string;
  sensorLocation?: string;
  lotCode?: string;
  batchId?: string;
  unit?: 'F' | 'C';
  includeAging?: boolean;
  includeViolations?: boolean;
}

export interface LotAgingAlertOptions {
  thresholdDays?: number;
  storageAreaName?: string;
  includeTemperature?: boolean;
  lotCodes?: string[];
}

export interface LotTemperatureViolationOptions {
  lotCodes?: string[];
  batchIds?: string[];
  includeStorageContext?: boolean;
}

export interface LotTemperatureServiceOptions {
  supabaseClient?: SupabaseClient;
  serviceRoleClient?: SupabaseClient;
}

interface SensorWithArea {
  id: string;
  sensor_id: string;
  name: string | null;
  location_description: string | null;
  storage_area_id: string | null;
  custom_temp_min_celsius: number | null;
  custom_temp_max_celsius: number | null;
  custom_humidity_min: number | null;
  custom_humidity_max: number | null;
  battery_level: number | null;
  signal_strength: number | null;
  last_seen_at: string | null;
  storage_areas: {
    id: string;
    name: string;
    area_type: string | null;
    level: string | null;
    location: string | null;
    temp_min_celsius: number | null;
    temp_max_celsius: number | null;
    temp_min_fahrenheit: number | null;
    temp_max_fahrenheit: number | null;
    temp_unit: 'celsius' | 'fahrenheit' | null;
  } | null;
}

interface TemperatureReadingRow {
  sensor_id: string;
  temp_celsius: number | null;
  humidity: number | null;
  recorded_at: string;
}

interface LotRow {
  id: string;
  lot_number: string;
  product_name: string | null;
  created_at: string;
  expiry_date: string | null;
}

interface BatchRow {
  id: string;
  lot_id: string | null;
  batch_number: string;
  product_name: string | null;
  quantity: number | null;
  created_at: string;
  expiry_date: string | null;
}

interface BatchTrackingRow {
  id: string;
  batch_id: string;
  storage_area_id: string | null;
  sensor_id: string | null;
  updated_at: string | null;
}

interface StorageAreaRow {
  id: string;
  name: string;
  area_type: string | null;
  level: string | null;
  location: string | null;
  temp_min_celsius: number | null;
  temp_max_celsius: number | null;
  temp_min_fahrenheit: number | null;
  temp_max_fahrenheit: number | null;
  temp_unit: 'celsius' | 'fahrenheit' | null;
}

const toFahrenheit = (value: number | null, unit: 'F' | 'C'): number | null => {
  if (value == null) return null;
  return unit === 'F' ? value : cToF(value);
};

const calculateAgeInDays = (createdAt: string | null | undefined): number => {
  if (!createdAt) return 0;
  const createdDate = new Date(createdAt);
  const now = new Date();
  const diff = now.getTime() - createdDate.getTime();
  return Math.max(0, Math.floor(diff / (1000 * 60 * 60 * 24)));
};

const determineAgingStatus = (ageInDays: number, expiryDate: string | null): LotStorageDetail['agingStatus'] => {
  if (expiryDate && new Date(expiryDate).getTime() < Date.now()) {
    return 'expired';
  }

  if (ageInDays >= CRITICAL_THRESHOLD_DAYS) {
    return 'critical';
  }

  if (ageInDays >= WARNING_THRESHOLD_DAYS) {
    return 'warning';
  }

  return 'fresh';
};

const buildLotDetail = (params: {
  lot: LotRow;
  batch: BatchRow | null;
  tracking: BatchTrackingRow | null;
  storageArea: StorageAreaRow | null;
  sensor: SensorWithArea | null;
}): LotStorageDetail => {
  const createdAt = params.batch?.created_at ?? params.lot.created_at;
  const ageInDays = calculateAgeInDays(createdAt);
  const expiryDate = params.batch?.expiry_date ?? params.lot.expiry_date;
  const agingStatus = determineAgingStatus(ageInDays, expiryDate);

  return {
    lotId: params.lot.id,
    lotCode: params.lot.lot_number,
    batchId: params.batch?.id ?? null,
    productName: params.batch?.product_name ?? params.lot.product_name ?? null,
    quantity: params.batch?.quantity ?? null,
    unit: null,
    createdAt,
    receivedAt: params.batch?.created_at ?? null,
    expiresAt: expiryDate,
    daysAged: ageInDays,
    agingStatus,
  } satisfies LotStorageDetail;
};

const buildAgingAlert = (
  detail: LotStorageDetail,
  storageArea: StorageAreaRow | null,
  sensor: SensorWithArea | null,
  thresholdDays: number,
  status: LotAgingAlert['status']
): LotAgingAlert => {
  const areaName = storageArea?.name ?? sensor?.storage_areas?.name ?? null;
  return {
    lotId: detail.lotId,
    lotCode: detail.lotCode,
    batchId: detail.batchId ?? null,
    storageAreaId: storageArea?.id ?? sensor?.storage_area_id ?? null,
    storageAreaName: areaName,
    status,
    daysAged: detail.daysAged ?? 0,
    thresholdDays,
    recommendedAction:
      status === 'critical'
        ? 'Inspect immediately for quality and safety'
        : status === 'expired'
        ? 'Remove from inventory; lot is past hold date'
        : 'Schedule inspection; lot is nearing aging threshold',
    productName: detail.productName ?? null,
    quantity: detail.quantity ?? null,
    unit: detail.unit ?? null,
    expiresAt: detail.expiresAt ?? null,
    temperatureContext: undefined,
  } satisfies LotAgingAlert;
};

const buildViolationImpact = (
  sensor: SensorWithArea,
  reading: TemperatureReadingRow,
  severity: TemperatureViolationImpact['severity'],
  lots: LotStorageDetail[],
  threshold: { min?: number | null; max?: number | null; unit: 'C' | 'F' }
): TemperatureViolationImpact => {
  const readingC = reading.temp_celsius ?? null;
  const readingF = readingC != null ? cToF(readingC) : null;
  const thresholdC = threshold.max ?? threshold.min ?? null;
  const thresholdF = thresholdC != null ? cToF(thresholdC) : null;

  return {
    sensorId: sensor.sensor_id,
    severity,
    recordedAt: reading.recorded_at,
    readingC,
    readingF,
    thresholdC,
    thresholdF,
    storageAreaId: sensor.storage_area_id,
    storageAreaName: sensor.storage_areas?.name ?? null,
    affectedLots: lots.map((lot) => ({
      lotId: lot.lotId,
      lotCode: lot.lotCode ?? null,
      batchId: lot.batchId ?? null,
    })),
  } satisfies TemperatureViolationImpact;
};

const resolveServiceClient = (options?: LotTemperatureServiceOptions): SupabaseClient => {
  if (options?.serviceRoleClient) {
    return options.serviceRoleClient;
  }

  if (options?.supabaseClient) {
    return options.supabaseClient;
  }

  return supabaseService;
};

const createLotTemperatureServiceInternal = (options?: LotTemperatureServiceOptions) => {
  const client = resolveServiceClient(options);

  const fetchSensorsWithAreas = async (filters: {
    storageAreaName?: string;
    sensorLocation?: string;
    storageTypes?: string[];
  }): Promise<SensorWithArea[]> => {
    let query = client
      .from('sensors')
      .select(
        `
        id,
        sensor_id,
        name,
        location_description,
        storage_area_id,
        custom_temp_min_celsius,
        custom_temp_max_celsius,
        custom_humidity_min,
        custom_humidity_max,
        battery_level,
        signal_strength,
        last_seen_at,
        storage_areas:storage_area_id (
          id,
          name,
          area_type,
          level,
          location,
          temp_min_celsius,
          temp_max_celsius,
          temp_min_fahrenheit,
          temp_max_fahrenheit,
          temp_unit
        )
      `,
      )
      .eq('is_active', true);

    if (filters.storageAreaName) {
      query = query.ilike('storage_areas.name', `%${filters.storageAreaName}%`);
    }

    if (filters.sensorLocation) {
      query = query.or(
        `name.ilike.%${filters.sensorLocation}%,location_description.ilike.%${filters.sensorLocation}%`
      );
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch sensors: ${error.message}`);
    }

    const rawRows = (data ?? []) as unknown as (Omit<SensorWithArea, 'temp_min_threshold' | 'temp_max_threshold' | 'humidity_min_threshold' | 'humidity_max_threshold'> & {
      custom_temp_min_celsius: number | null;
      custom_temp_max_celsius: number | null;
      custom_humidity_min: number | null;
      custom_humidity_max: number | null;
    })[];

    // Transform database column names to expected property names for backward compatibility
    const rows: SensorWithArea[] = rawRows.map(row => ({
      ...row,
      temp_min_threshold: row.custom_temp_min_celsius,
      temp_max_threshold: row.custom_temp_max_celsius,
      humidity_min_threshold: row.custom_humidity_min,
      humidity_max_threshold: row.custom_humidity_max,
      // Retain the custom_* fields explicitly for violation detection
      custom_temp_min_celsius: row.custom_temp_min_celsius,
      custom_temp_max_celsius: row.custom_temp_max_celsius,
      custom_humidity_min: row.custom_humidity_min,
      custom_humidity_max: row.custom_humidity_max,
    }));

    if (filters.storageTypes && filters.storageTypes.length > 0) {
      return rows.filter((row) =>
        filters.storageTypes?.some((type) => row.storage_areas?.area_type === type)
      );
    }

    return rows;
  };

  const fetchLatestReadings = async (sensorIds: string[]): Promise<Map<string, TemperatureReadingRow>> => {
    if (sensorIds.length === 0) {
      return new Map();
    }

    const { data, error } = await client
      .from('temperature_readings')
      .select('sensor_id, temp_celsius, humidity, recorded_at')
      .in('sensor_id', sensorIds)
      .order('recorded_at', { ascending: false })
      .limit(sensorIds.length * 5);

    if (error) {
      throw new Error(`Failed to fetch temperature readings: ${error.message}`);
    }

    const rows = (data ?? []) as unknown as TemperatureReadingRow[];
    const readings = new Map<string, TemperatureReadingRow>();

    for (const row of rows) {
      if (!readings.has(row.sensor_id)) {
        readings.set(row.sensor_id, row);
      }
    }

    return readings;
  };

  const fetchLotData = async () => {
    const [lotsRes, batchesRes, trackingRes, storageAreaRes] = await Promise.all([
      client
        .from('lots')
        .select('id, lot_number, product_name, created_at, expiry_date'),
      client
        .from('batches')
        .select('id, lot_id, batch_number, product_name, quantity, created_at, expiry_date'),
      client
        .from('batch_tracking')
        .select('id, batch_id, storage_area_id, sensor_id, updated_at'),
      client
        .from('storage_areas')
        .select('id, name, area_type, level, location, temp_min_celsius, temp_max_celsius, temp_min_fahrenheit, temp_max_fahrenheit, temp_unit'),
    ]);

    if (lotsRes.error) throw new Error(`Failed to fetch lots: ${lotsRes.error.message}`);
    if (batchesRes.error) throw new Error(`Failed to fetch batches: ${batchesRes.error.message}`);
    if (trackingRes.error) throw new Error(`Failed to fetch batch tracking: ${trackingRes.error.message}`);
    if (storageAreaRes.error) throw new Error(`Failed to fetch storage areas: ${storageAreaRes.error.message}`);

    return {
      lots: (lotsRes.data ?? []) as unknown as LotRow[],
      batches: (batchesRes.data ?? []) as unknown as BatchRow[],
      tracking: (trackingRes.data ?? []) as unknown as BatchTrackingRow[],
      storageAreas: new Map<string, StorageAreaRow>(
        ((storageAreaRes.data ?? []) as unknown as StorageAreaRow[]).map((area) => [area.id, area])
      ),
    };
  };

  const compileStorageSummaries = async (
    options: StorageAreaLotSummaryOptions
  ): Promise<StorageAreaTemperatureSummary[]> => {
    const sensors = await fetchSensorsWithAreas({
      storageAreaName: options.storageAreaName,
      sensorLocation: options.sensorLocation,
      storageTypes: undefined,
    });

    const sensorIdMap = new Map<string, SensorWithArea>();
    sensors.forEach((sensor) => {
      sensorIdMap.set(sensor.id, sensor);
    });

    const readings = await fetchLatestReadings(sensors.map((sensor) => sensor.sensor_id));
    const lotData = await fetchLotData();

    const batchesByLot = new Map<string, BatchRow[]>();
    lotData.batches.forEach((batch) => {
      if (!batch.lot_id) return;
      const existing = batchesByLot.get(batch.lot_id) ?? [];
      existing.push(batch);
      batchesByLot.set(batch.lot_id, existing);
    });

    const trackingByBatch = new Map<string, BatchTrackingRow[]>();
    lotData.tracking.forEach((entry) => {
      const existing = trackingByBatch.get(entry.batch_id) ?? [];
      existing.push(entry);
      trackingByBatch.set(entry.batch_id, existing);
    });

    const storageSummaries = new Map<string, StorageAreaTemperatureSummary>();

    const getOrCreateSummary = (sensor: SensorWithArea): StorageAreaTemperatureSummary => {
      const storageArea = sensor.storage_areas;
      const areaId = storageArea?.id ?? sensor.storage_area_id ?? sensor.sensor_id;
      const key = areaId ?? `sensor-${sensor.sensor_id}`;

      if (!storageSummaries.has(key)) {
        storageSummaries.set(key, {
          storageArea: {
            id: areaId,
            name: storageArea?.name ?? sensor.name ?? sensor.location_description ?? 'Unassigned storage',
            type: storageArea?.area_type ?? null,
            level: storageArea?.level ?? null,
            locationDescription: storageArea?.location ?? sensor.location_description ?? null,
          },
          sensors: [],
          latestTemperatureF: null,
          latestTemperatureC: null,
          latestHumidity: null,
          latestRecordedAt: null,
          lots: [],
          agingAlerts: [],
          temperatureViolations: [],
        });
      }

      return storageSummaries.get(key)!;
    };

    for (const sensor of sensors) {
      const summary = getOrCreateSummary(sensor);
      const reading = readings.get(sensor.sensor_id) ?? null;
      const unit = options.unit ?? 'F';
      const tempC = reading?.temp_celsius ?? null;
      const tempF = tempC != null ? cToF(tempC) : null;

      summary.sensors.push({
        sensorId: sensor.sensor_id,
        sensorName: sensor.name,
        location: sensor.location_description,
        latestReading: unit === 'F' ? tempF : tempC,
        latestRecordedAt: reading?.recorded_at ?? sensor.last_seen_at,
        unit,
        status: 'normal',
        alertCount: 0,
      });

      if (!summary.latestRecordedAt || (reading && reading.recorded_at > summary.latestRecordedAt)) {
        summary.latestRecordedAt = reading?.recorded_at ?? sensor.last_seen_at;
        summary.latestTemperatureC = tempC;
        summary.latestTemperatureF = tempF;
        summary.latestHumidity = reading?.humidity ?? null;
      }
    }

    for (const lot of lotData.lots) {
      const linkedBatches = batchesByLot.get(lot.id) ?? [];
      if (linkedBatches.length === 0) {
        continue;
      }

      for (const batch of linkedBatches) {
        const trackEntries = trackingByBatch.get(batch.id) ?? [
          {
            id: `${batch.id}-untracked`,
            batch_id: batch.id,
            storage_area_id: null,
            sensor_id: null,
            updated_at: batch.created_at,
          },
        ];

        for (const tracking of trackEntries) {
          if (options.lotCode && lot.lot_number !== options.lotCode) {
            continue;
          }
          if (options.batchId && batch.id !== options.batchId) {
            continue;
          }

          const storageArea = tracking.storage_area_id
            ? lotData.storageAreas.get(tracking.storage_area_id) ?? null
            : null;
          const sensor = tracking.sensor_id ? sensorIdMap.get(tracking.sensor_id) ?? null : null;

          const summary = sensor
            ? getOrCreateSummary(sensor)
            : storageArea
            ? (() => {
                const key = storageArea.id;
                if (!storageSummaries.has(key)) {
                  storageSummaries.set(key, {
                    storageArea: {
                      id: storageArea.id,
                      name: storageArea.name,
                      type: storageArea.area_type ?? null,
                      level: storageArea.level ?? null,
                      locationDescription: storageArea.location ?? null,
                    },
                    sensors: [],
                    latestTemperatureF: null,
                    latestTemperatureC: null,
                    latestHumidity: null,
                    latestRecordedAt: null,
                    lots: [],
                    agingAlerts: [],
                    temperatureViolations: [],
                  });
                }
                return storageSummaries.get(key)!;
              })()
            : null;

          if (!summary) {
            continue;
          }

          const sensorReading = sensor ? readings.get(sensor.sensor_id) ?? null : null;

          const detail = buildLotDetail({
            lot,
            batch,
            tracking,
            storageArea,
            sensor,
          });

          summary.lots.push(detail);

          if (options.includeAging !== false) {
            const status = detail.agingStatus ?? 'fresh';
            if (status === 'warning' || status === 'critical') {
              const alert = buildAgingAlert(
                detail,
                storageArea,
                sensor,
                status === 'critical' ? CRITICAL_THRESHOLD_DAYS : WARNING_THRESHOLD_DAYS,
                status === 'critical' ? 'critical' : 'warning'
              );

              if (sensorReading) {
                alert.temperatureContext = {
                  latestReadingF: sensorReading.temp_celsius != null ? cToF(sensorReading.temp_celsius) : null,
                  latestReadingC: sensorReading.temp_celsius,
                  lastRecordedAt: sensorReading.recorded_at,
                  severity: status === 'critical' ? 'critical' : 'warning',
                };
              }

              summary.agingAlerts.push(alert);
            }
            if (status === 'expired') {
              const alert = buildAgingAlert(detail, storageArea, sensor, CRITICAL_THRESHOLD_DAYS, 'expired');
              if (sensorReading) {
                alert.temperatureContext = {
                  latestReadingF: sensorReading.temp_celsius != null ? cToF(sensorReading.temp_celsius) : null,
                  latestReadingC: sensorReading.temp_celsius,
                  lastRecordedAt: sensorReading.recorded_at,
                  severity: 'critical',
                };
              }
              summary.agingAlerts.push(alert);
            }
          }
        }
      }
    }

    if (options.includeViolations !== false) {
      for (const [key, summary] of storageSummaries) {
        for (const sensor of summary.sensors) {
          const sensorRow = sensors.find((row) => row.sensor_id === sensor.sensorId);
          if (!sensorRow) continue;

          const reading = readings.get(sensorRow.sensor_id);
          if (!reading || reading.temp_celsius == null) continue;

          const tempC = reading.temp_celsius;
          const minThreshold = sensorRow.custom_temp_min_celsius;
          const maxThreshold = sensorRow.custom_temp_max_celsius;

          if (maxThreshold != null && tempC > maxThreshold) {
            summary.temperatureViolations.push(
              buildViolationImpact(sensorRow, reading, 'critical', summary.lots, {
                max: maxThreshold,
                unit: 'C',
              })
            );
          } else if (minThreshold != null && tempC < minThreshold) {
            summary.temperatureViolations.push(
              buildViolationImpact(sensorRow, reading, 'warning', summary.lots, {
                min: minThreshold,
                unit: 'C',
              })
            );
          }
        }
      }
    }

    return Array.from(storageSummaries.values());
  };

  const queryTemperatureWithLots = async (
    options: TemperatureLotQueryOptions
  ): Promise<StorageAreaTemperatureSummary[]> => {
    const summaries = await compileStorageSummaries({
      storageAreaName: options.search,
      sensorLocation: options.search,
      unit: options.unit ?? 'F',
      includeAging: options.includeAging,
      includeViolations: options.includeViolations,
    });

    const filtered = summaries.filter((summary) => {
      if (options.storageTypes && options.storageTypes.length > 0) {
        const type = summary.storageArea.type ?? '';
        if (!options.storageTypes.includes(type)) {
          return false;
        }
      }

      if (options.minimumFreezerTempCelsius != null && summary.latestTemperatureC != null) {
        return summary.latestTemperatureC <= options.minimumFreezerTempCelsius;
      }

      return true;
    });

    return filtered;
  };

  const getStorageAreaLotSummary = async (
    options: StorageAreaLotSummaryOptions
  ): Promise<StorageAreaTemperatureSummary[]> => {
    return compileStorageSummaries(options);
  };

  const getLotAgingAlerts = async (options: LotAgingAlertOptions): Promise<LotAgingAlert[]> => {
    const summaries = await compileStorageSummaries({
      storageAreaName: options.storageAreaName,
      unit: 'F',
      includeAging: true,
      includeViolations: options.includeTemperature ?? true,
    });

    const alerts = summaries.flatMap((summary) => summary.agingAlerts ?? []);

    const filteredAlerts = options.lotCodes && options.lotCodes.length > 0
      ? alerts.filter((alert) => alert.lotCode && options.lotCodes?.includes(alert.lotCode))
      : alerts;

    return filteredAlerts.filter((alert) => {
      if (!options.thresholdDays) return true;
      return alert.daysAged >= options.thresholdDays;
    });
  };

  const getTemperatureViolationsForLots = async (
    options: LotTemperatureViolationOptions
  ): Promise<StorageAreaTemperatureSummary[]> => {
    const summaries = await compileStorageSummaries({
      unit: 'F',
      includeAging: true,
      includeViolations: true,
    });

    if (!options.lotCodes && !options.batchIds) {
      return summaries.filter((summary) => (summary.temperatureViolations?.length ?? 0) > 0);
    }

    return summaries
      .map((summary) => {
        const matchingLots = summary.lots.filter((lot) => {
          const lotMatch = options.lotCodes?.includes(lot.lotCode ?? '') ?? false;
          const batchMatch = options.batchIds?.includes(lot.batchId ?? '') ?? false;
          return lotMatch || batchMatch;
        });

        if (matchingLots.length === 0) {
          return null;
        }

        const violations = summary.temperatureViolations?.filter((violation) =>
          violation.affectedLots.some((lotRef) =>
            (options.lotCodes && lotRef.lotCode && options.lotCodes.includes(lotRef.lotCode)) ||
            (options.batchIds && lotRef.batchId && options.batchIds.includes(lotRef.batchId))
          )
        ) ?? [];

        return {
          ...summary,
          lots: matchingLots,
          temperatureViolations: violations,
        } satisfies StorageAreaTemperatureSummary;
      })
      .filter((summary): summary is StorageAreaTemperatureSummary => summary != null);
  };

  return {
    queryTemperatureWithLots,
    getStorageAreaLotSummary,
    getLotAgingAlerts,
    getTemperatureViolationsForLots,
  };
};

const defaultService = createLotTemperatureServiceInternal();

export const queryTemperatureWithLots = defaultService.queryTemperatureWithLots;
export const getStorageAreaLotSummary = defaultService.getStorageAreaLotSummary;
export const getLotAgingAlerts = defaultService.getLotAgingAlerts;
export const getTemperatureViolationsForLots = defaultService.getTemperatureViolationsForLots;

export const createLotTemperatureService = (options?: LotTemperatureServiceOptions) =>
  createLotTemperatureServiceInternal(options);

export default defaultService;
