{"permissions": {"allow": ["Bash(npm run:*)", "Bash(npx tsc:*)", "Bash(git branch:*)", "Bash(npm audit:*)", "Bash(find:*)", "Bash(npm install)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "Bash(npm:*)", "Bash(npx supabase:*)", "<PERSON><PERSON>(mv:*)", "Bash(if [ -f .env ])", "<PERSON>sh(then echo \"Found .env file\")", "Bash(else echo \"No .env file found\")", "Bash(fi)", "Bash(lsof:*)", "Bash(kill:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(grep:*)", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_fill", "Bash(python3 -m pip install:*)", "Bash(uvx:*)", "mcp__ide__getDiagnostics", "mcp__sequential-thinking__sequentialthinking", "Bash(npx eslint:*)", "mcp__serena__read_file", "mcp__serena__activate_project", "mcp__serena__replace_regex", "mcp__serena__search_for_pattern", "mcp__serena__execute_shell_command", "Bash(npx vitest run:*)", "Bash(npx update-browserslist-db:*)", "Bash(npx vite-bundle-analyzer:*)", "Bash(npx rollup-plugin-visualizer:*)", "Bash(npx @rollup/plugin-node-resolve:*)", "WebFetch(domain:tempstickapi.com)", "WebSearch", "mcp__fetch__fetch", "mcp__serena__list_dir", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__write_memory", "mcp__serena__find_file", "mcp__serena__create_text_file", "mcp__serena__insert_after_symbol", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__replace_symbol_body", "<PERSON><PERSON>(sed:*)", "mcp__serena__get_symbols_overview", "mcp__serena__think_about_collected_information", "Bash(git checkout:*)", "Bash(git pull:*)", "Bash(git merge:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(source .env)", "<PERSON><PERSON>(curl:*)", "Bash(supabase db reset:*)", "WebFetch(domain:github.com)", "mcp__serena__think_about_task_adherence", "mcp__serena__read_memory", "mcp__serena__find_symbol", "mcp__puppeteer__puppeteer_navigate", "Bash(npx puppeteer browsers:*)", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_evaluate", "Bash(supabase db:*)", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE3NTU3MTgsImV4cCI6MjA1NzMzMTcxOH0.zC8AN8oLj_7QJrMFiqiyze-IZz2nNcyFpMFGUvMQF48 node verify-tempstick-tables.js)", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTc1NTcxOCwiZXhwIjoyMDU3MzMxNzE4fQ.hpZaedCRzTG-k242vLeAG2QPSmjzLiU-3f2eKq9rvbk node execute-tempstick-sql.js)", "Bash(supabase status:*)", "Bash(supabase projects:*)", "Bash(supabase migration new:*)", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTc1NTcxOCwiZXhwIjoyMDU3MzMxNzE4fQ.hpZaedCRzTG-k242vLeAG2QPSmjzLiU-3f2eKq9rvbk node create-tempstick-tables-direct.js)", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTc1NTcxOCwiZXhwIjoyMDU3MzMxNzE4fQ.hpZaedCRzTG-k242vLeAG2QPSmjzLiU-3f2eKq9rvbk node verify-tempstick-tables.js)", "Bash(npx @puppeteer/browsers install:*)", "<PERSON><PERSON>(claude doctor)", "mcp__serena__list_memories", "mcp__serena__switch_modes", "Bash(git log:*)", "Bash(git fetch:*)", "Bash(psql:*)", "mcp__github__search_code", "Bash(rm:*)", "mcp__serena__find_referencing_symbols", "Bash(git stash:*)", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTc1NTcxOCwiZXhwIjoyMDU3MzMxNzE4fQ.hpZaedCRzTG-k242vLeAG2QPSmjzLiU-3f2eKq9rvbk psql postgresql://postgres:<EMAIL>:5432/postgres -c \"\nSELECT schemaname, tablename, policyname, permissive, roles, cmd, qual \nFROM pg_policies \nWHERE tablename IN (''sensors'', ''temperature_readings'') \nORDER BY tablename, policyname;\n\")", "mcp__supabase__get_project", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTc1NTcxOCwiZXhwIjoyMDU3MzMxNzE4fQ.hpZaedCRzTG-k242vLeAG2QPSmjzLiU-3f2eKq9rvbk node fix-missing-sensors.js)", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTc1NTcxOCwiZXhwIjoyMDU3MzMxNzE4fQ.hpZaedCRzTG-k242vLeAG2QPSmjzLiU-3f2eKq9rvbk TEMPSTICK_SYNC_USER_ID=596a1bc9-8c6c-49b5-affc-6720f24f7572 node import-historical-tempstick-data.js)", "mcp__shadcn__search_items_in_registries", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTc1NTcxOCwiZXhwIjoyMDU3MzMxNzE4fQ.hpZaedCRzTG-k242vLeAG2QPSmjzLiU-3f2eKq9rvbk node -e \"\nimport { createClient } from ''@supabase/supabase-js'';\nconst supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.VITE_SUPABASE_SERVICE_ROLE_KEY);\nconst { data, error } = await supabase.from(''temperature_readings'').select(''recorded_at, temp_celsius, sensors(name)'').order(''recorded_at'', { ascending: false }).limit(3);\nif (error) console.error(''Error:'', error.message); else console.log(''Latest readings:'', data.map(r => ({sensor: r.sensors?.name, temp: r.temp_celsius + ''°C'', time: r.recorded_at})));\n\")", "Bash(git push:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(ps:*)", "Bash(VITE_SUPABASE_URL=https://puzjricwpsjusjlgrwen.supabase.co VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTc1NTcxOCwiZXhwIjoyMDU3MzMxNzE4fQ.hpZaedCRzTG-k242vLeAG2QPSmjzLiU-3f2eKq9rvbk psql postgresql://postgres:<EMAIL>:5432/postgres -c \"SELECT bucketname, policyname, definition FROM storage.policies WHERE bucketname = ''inventory-images'';\")", "mcp__serena__get_current_config", "<PERSON><PERSON>(python3:*)", "WebFetch(domain:openai.github.io)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(docker inspect:*)", "<PERSON><PERSON>(docker start:*)", "<PERSON><PERSON>(docker stop:*)", "Bash(docker logs:*)", "Bash(npx @byterover/cipher:*)", "Read(//opt/homebrew/lib/node_modules/@byterover/cipher/dist/**)", "Read(//opt/homebrew/lib/node_modules/@byterover/cipher/**)", "mcp__cipher__ask_cipher", "<PERSON><PERSON>(docker ps:*)", "Bash(docker kill:*)", "Read(//Users/<USER>/.codex/**)", "Read(//Users/<USER>/.codeium/windsurf/**)", "Bash(supabase stop:*)", "<PERSON><PERSON>(supabase start:*)", "<PERSON><PERSON>(docker:*)", "Bash(pnpm vitest run:*)", "Bash(VITE_SUPABASE_URL=http://127.0.0.1:54321 SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU node scripts/seed-categories.mjs)", "<PERSON><PERSON>(open:*)", "Bash(VITE_SUPABASE_URL=http://127.0.0.1:54321 SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU node scripts/create-test-user.js)", "mcp__byterover-mcp__byterover-retrieve-knowledge"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/.cache/puppeteer"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["shadcn", "serena", "cipher"]}