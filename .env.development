# Local development environment variables
# Copy from .env.example and populate any secrets in a non-committed override file if required.
#
# This file is configured for local Supabase Docker development.
# Ensure you have started Supabase locally: supabase start

# -----------------------------------------------------------------------------
# CLIENT_VISIBLE - Variables prefixed with VITE_ are exposed to the browser
# -----------------------------------------------------------------------------

# Supabase Configuration - Local Docker Instance
# These are the default keys for local Supabase Docker development
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# API Configuration - Local Development
VITE_API_BASE_URL=http://127.0.0.1:3001

# TempStick Configuration - Local Proxy Development
# Points to local CORS proxy server for TempStick API integration
VITE_TEMPSTICK_API_URL=http://127.0.0.1:3001/api/v1
VITE_TEMPSTICK_API_KEY=local-tempstick-api-key
# VITE_TEMPSTICK_WEBHOOK_URL=
VITE_ENVIRONMENT=development
VITE_TEMPSTICK_SYNC_INTERVAL=300000
VITE_TEMPSTICK_RATE_LIMIT=30
VITE_TEMPSTICK_MAX_RETRIES=2
VITE_FEATURE_DIRECT_REALTIME=false
VITE_FEATURE_USE_WEBRTC=true
VITE_FEATURE_OVERLAY_CLEANUP=false
VITE_FEATURE_OVERLAY_DIAGNOSTIC=false
VITE_FEATURE_BOTTOM_BAR_DEBUG=false
VITE_SKIP_AUTH=false
VITE_REALTIME_MODEL=gpt-4o-realtime-preview-2024-12-17
VITE_REALTIME_VOICE=alloy
VITE_DATADOG_API_KEY=
VITE_DATADOG_APP_KEY=
# VITE_SENTRY_DSN=
VITE_SMTP_HOST=
VITE_SMTP_PORT=587
VITE_SMTP_USERNAME=
VITE_SMTP_PASSWORD=
VITE_SMTP_FROM=<EMAIL>
VITE_SMS_PROVIDER=twilio
VITE_TWILIO_ACCOUNT_SID=
VITE_TWILIO_AUTH_TOKEN=
VITE_TWILIO_FROM_NUMBER=
# VITE_SLACK_WEBHOOK_URL=
VITE_SLACK_BOT_TOKEN=

# -----------------------------------------------------------------------------
# SERVER_ONLY - Variables NOT prefixed with VITE_ are server-side only
# -----------------------------------------------------------------------------

# Supabase Service Role Key - Local Docker Instance
# This is the default service role key for local Supabase Docker development
# Used for admin operations that bypass Row Level Security (RLS)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# OpenAI API Key - Required for voice processing features
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=

# TempStick Sync Configuration - Required for TempStick data synchronization
# This should be a valid user UUID from your local Supabase users table
# Create a user account in your local development database and use their UUID here
TEMPSTICK_SYNC_USER_ID=fa4604f7-567a-442d-b386-ecc1fc0cc727
